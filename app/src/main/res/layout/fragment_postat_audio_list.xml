<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="viewModel"
            type="com.app.messej.ui.home.publictab.postat.create.CreatePostatViewModel" />
        <import type="com.app.messej.data.model.enums.PostatMediaTab"/>
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.home.publictab.postat.create.PostatAudioListFragment">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fitsSystemWindows="true"
            android:background="@color/colorSurface">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/header"
                android:layout_marginTop="@dimen/line_spacing"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/close_button"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_margin="@dimen/activity_margin"
                    android:clickable="true"
                    android:src="@drawable/ic_caret_left"
                    android:tint="@color/textColorSecondary"
                    android:foreground="?attr/selectableItemBackground"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/new_post"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/activity_margin"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/postat_select_music"
                    android:textAppearance="@style/TextAppearance.Flashat.Headline6"
                    android:textColor="@color/textColorSecondary"
                    app:layout_constraintBottom_toBottomOf="@+id/close_button"
                    app:layout_constraintEnd_toStartOf="@+id/done_button"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/close_button"
                    app:layout_constraintTop_toTopOf="@+id/close_button"
                    app:layout_constraintVertical_chainStyle="packed" />


                <com.google.android.material.button.MaterialButton
                    android:id="@+id/done_button"
                    style="@style/Widget.Flashat.Button.TextButton.IconOnly.Inverse"
                    android:textColor="@color/colorPrimary"
                    android:textAllCaps="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/postat_music_selected"
                    android:backgroundTint="@color/colorSecondary"
                    app:layout_constraintBottom_toBottomOf="@+id/new_post"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/new_post" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/original_audio_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:layout_marginStart="@dimen/activity_margin"
                android:layout_marginTop="@dimen/activity_margin"
                android:text="@string/postat_use_original_audio"
                android:textAppearance="@style/TextAppearance.Flashat.Headline6"
                android:textColor="@color/textColorSecondary"
                app:layout_constraintTop_toBottomOf="@id/header"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/original_audio_switch"
                app:layout_constraintHorizontal_chainStyle="spread" />

            <com.google.android.material.switchmaterial.SwitchMaterial
                android:id="@+id/original_audio_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/activity_margin"
                android:foreground="?attr/selectableItemBackground"
                android:src="@drawable/ic_caret_right"
                android:checked="@{viewModel.useOriginalAudio}"
                app:layout_constraintHorizontal_chainStyle="spread"
                app:layout_constraintStart_toEndOf="@id/original_audio_text"
                app:layout_constraintBottom_toBottomOf="@+id/original_audio_text"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/original_audio_text" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/search_box"
                style="@style/Widget.Flashat.WhiteTextInput.LocationSearch"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:hintAnimationEnabled="false"
                app:hintEnabled="false"
                app:startIconDrawable="@drawable/ic_search_lens"
                android:layout_marginHorizontal="@dimen/activity_margin"
                android:layout_marginTop="@dimen/activity_margin"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/original_audio_text">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/search_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/postat_search_music"
                    android:inputType="textNoSuggestions"
                    android:text="@={viewModel.searchKeyword}" />

            </com.google.android.material.textfield.TextInputLayout>
            <com.kennyc.view.MultiStateView
                android:id="@+id/multiStateView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/search_box"
                app:msv_emptyView="@layout/layout_list_state_empty"
                app:msv_errorView="@layout/layout_list_state_error"
                app:layout_constraintBottom_toBottomOf="parent"
                app:msv_loadingView="@layout/layout_eds_state_loading_chat_list"
                app:msv_viewState="content">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/music_list"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/line_spacing"
                    android:minHeight="80dp"
                    android:padding="@dimen/line_spacing"
                    tools:listitem="@layout/item_postat_music_list" />

            </com.kennyc.view.MultiStateView>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>
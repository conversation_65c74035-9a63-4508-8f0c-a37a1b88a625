<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="frp"
            type="com.app.messej.ui.home.CommonHomeViewModel.FlaxRatePackage" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="2dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:goneIf="@{frp==null}"
        android:background="@drawable/bg_header_rate"
        android:backgroundTint="@color/colorSurfaceSecondary"
        android:clickable="true"
        android:focusable="true">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/current_flax_rate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            app:layout_goneMarginEnd="@dimen/line_spacing"
            style="@style/TextAppearance.Flashat.Caption.Bold"
            android:textSize="10sp"
            android:text="@{@string/common_percentage_value(frp.flaxRate)}"
            android:textColor="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/current_flax_state"
            tools:text="20%" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/current_flax_state"
            app:goneIf="@{frp.rateChange==null}"
            tools:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/line_spacing"
            android:layout_marginStart="2dp"
            android:src="@{frp.rateChange==true?@drawable/ic_flax_state_up:@drawable/ic_flax_state_down}"
            tools:src="@drawable/ic_flax_state_down"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
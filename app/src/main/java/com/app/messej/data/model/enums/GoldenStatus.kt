package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class GoldenStatus {
    @SerializedName("active") ACTIVE,
    @SerializedName("expired")  EXPIRED,
    @SerializedName("cancelled") CANCELLED;

    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}
package com.app.messej.data.model.enums

enum class DocumentType(val type: String) {
    TERMS_OF_USE("terms_of_use"),
    USERNAME_GUIDE_LINES("username_guidelines"),
    COMMUNITY_GUIDE_LINES("community_guidelines"),
    PRIVACY_POLICY("privacy_policy"),
    COOKIES_POLICY("cookies_policy"),
    HUDDLE_POLICY("huddle_policy"),
    GROUP_POLICY("group_policy"),
    HOME_APP_BUSINESS("home_app_business"),
    PP_RULES_REGULATIONS("pp_rules_and_regulations"),
    GIFT_POLICY("gift_policy"),
    PODIUM_POLICY("podium_policy"),
    FLAX_POLICY("pp_help"),
    HUDDLE_SELLING_PROCEDURE("huddle_selling_procedure"),
    PODIUM_CHALLENGE_LIKE_DETAILS("likes-challenge"),
    PODIUM_CHALLENGE_GIFT_DETAILS("gift-challenge"),
    PODIUM_CHALLENGE_FLAG_DETAILS("flags-challenge"),
    PODIUM_CHALLENGE_CONFOUR_DETAILS("con-four"),
    PODIUM_CHALLENGE_PENALTY_KICK_DETAILS("penalty-kick"),
    PODIUM_ABOUT_MAIDAN_CHALLENGE_DETAILS("about-maidan-challenge"),
    PODIUM_CHALLENGE_BOX_DETAILS("about-box-challenge"),
    PODIUM_CHALLENGE_KNOWLEDGE_RACE_DETAILS("about-knowledge-race"),
    LEGAL_AFFAIRS_ABOUT("about-legal-affairs"),
    STATE_AFFAIRS_ABOUT("about-state-affairs"),
    E_TRIBE_ABOUT("about-e-tribe"),

    //Used for Task tab documents
    TASK_TAB_ACCOUNT_STATUS("task-about-status"),
    TASK_TAB_PROFILE_ABOUT("task-profile"),
    TASK_TAB_HUDDLE_ABOUT("task-huddle"),
    TASK_TAB_E_TRIBE("task-e-tribe"),
    TASK_TAB_RATE_FLASHAT_ABOUT("task-rate-flashat"),
    TASK_TAB_PERFORMANCE_RATING_ABOUT("task-performance-rating"),
    TASK_TAB_MINIMUM_BALANCE_ABOUT("task-minimum-balance"),
    TASK_TAB_GENEROSITY_ABOUT("task-generosity"),
    TASK_TAB_ELIGIBILITY_ABOUT("task-eligibility"),

    ABOUT_FLASHAT("about-flashat"),
    FLIX_AND_COINS("flix-and-coins"),
    USERS_LEVELS("users-levels"),
    USERS_STRENGTH("users-strength"),
    PERSONAL_DATA("personal-data"),
    ID_CARD("about-id-card"),

    ABOUT_BUDDIES("about-buddies"),
    ABOUT_INTRUDERS("about-intruders"),

    ABOUT_FLASH("about-flash"),
    ABOUT_POSTAT("about-postat"),

    ABOUT_TASKS("about-tasks"),
    ABOUT_DEALS("about-deals"),
    ABOUT_STATEMENTS("about-statements"),

    ABOUT_PRESIDENTIAL_AFFAIRS("about-presidential-affairs"),
    ABOUT_SOCIAL_AFFAIRS("about-social-affairs"),
}
package com.app.messej.data.model.api

import com.app.messej.data.model.enums.GoldenStatus
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.DateTimeUtils.FORMAT_ISO_DATE_TIME
import com.google.gson.annotations.SerializedName
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime

data class UpgradeUserLevelResponse(
    @SerializedName("current_user_level") val currentUserLevel: UserCitizenship? = null,
    @SerializedName("user_upgrade_level_mapping") val userUpgradeLevelMapping: UserUpgradeLevelMapping,
    @SerializedName("is_golden_expired") val isGoldenExpired: Boolean? = null,
    @SerializedName("golden_expiry_date") var goldenExpiryDate: String? = null,
    @SerializedName("is_restore_minister_eligible") val isRestoreMinisterEligible: Boolean? = null,
    @SerializedName("is_auto_renewal") val isAutoRenewal: Boolean? = null,
    @SerializedName("status") val goldenStatus: GoldenStatus? = null,
){
    private val expireParsed: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTime(goldenExpiryDate, DateTimeUtils.FORMAT_ISO_DATE_TIME)

    val expireTime
        get() = DateTimeUtils.format(expireParsed, DateTimeUtils.FORMAT_DDMMYYYY_SLASHED)
}



package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.UserCitizenship
import com.google.gson.annotations.SerializedName

data class LevelUpgradationEvent(
    @SerializedName("userId") val userId: Int,
    @SerializedName("citizenship") var citizenship: UserCitizenship?,
    @SerializedName("user_level_animation_url") var userLevelAnimationUrl: String,
    @SerializedName("citizenship_priority") var citizenshipPriority: Int,
    @SerializedName("upgraded_user_id") var upgradedUserId: Int,
    @SerializedName("purchased") var purchased:Boolean?=false
) : SocketEventPayload()
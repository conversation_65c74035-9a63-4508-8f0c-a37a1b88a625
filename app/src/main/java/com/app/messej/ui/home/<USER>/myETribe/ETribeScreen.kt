package com.app.messej.ui.home.publictab.myETribe

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Divider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.unit.dp
import androidx.paging.compose.collectAsLazyPagingItems
import com.app.messej.R
import com.app.messej.data.model.api.eTribe.ETribeResponse.Companion.ADMIN_DELETED
import com.app.messej.data.model.enums.ETribeBannerView
import com.app.messej.data.model.enums.ETribeTabs
import com.app.messej.ui.composeComponents.ComposeShimmerListLayout
import com.app.messej.ui.composeComponents.CustomPaginationView
import com.app.messej.ui.composeComponents.ListEmptyItemView
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer

@Composable
fun ETribeScreen(
    viewModel: MyETribeViewModel,
    onSuperStarTribeClick: (Int) -> Unit,
    onUserProfileClick: (Int) -> Unit = {}
) {
    val currentSelectedTab by viewModel.selectedTab.observeAsState()
    val eTribeTabs = viewModel.tabs
    val eTribeList = viewModel.eTribeList.collectAsLazyPagingItems()
    val tribeDetail by viewModel.tribeDetail.observeAsState()
    val isEditTribeAlertBoxVisible by viewModel.isEditETribeAlertVisible.observeAsState()
    val countryFlagList by viewModel.countryFlagList.observeAsState()
    val eTribeBannerType by viewModel.eTribeBannerType.observeAsState()

    if (isEditTribeAlertBoxVisible == true) {
        EditETribeAlertView(
            state = viewModel.editTribeTextFieldState,
            onCancelClick = {
                viewModel.apply {
                    setEditTribeAlertBoxVisibility()
                    resetTribeName()
                }
            }, onSaveClick = viewModel::editTribeName
        )
    }

    Column(modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 10.dp)
    ) {
        AnimatedVisibility(
            modifier = Modifier.padding(top = dimensionResource(id = R.dimen.activity_margin)),
            visible = eTribeBannerType != null
        ) {
            if (eTribeBannerType == ETribeBannerView.SuperStarTribeView) {
                ETribeBannerView(
                    item = tribeDetail?.superStarTribeDetail,
                    onClick = onSuperStarTribeClick
                )
            } else {
                ETribeDeletedAndFreeUserBannerView(type = eTribeBannerType)
            }
        }

        CustomVerticalSpacer(space = R.dimen.activity_margin)

        if (tribeDetail?.tribeStatus == ADMIN_DELETED) {
            ListEmptyItemView(
                icon = R.drawable.ic_tribe_empty,
                textId = R.string.e_tribe_tribe_deleted_text
            )
            return@Column
        }

        Row(
            modifier = Modifier
                .height(intrinsicSize = IntrinsicSize.Max)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing))
        ) {
            repeat(eTribeTabs.size) {
                val tab = eTribeTabs[it]
                ETribeSelectionButton(
                    modifier = Modifier
                        .fillMaxHeight()
                        .fillMaxWidth()
                        .weight(weight = 1F),
                    tab = tab,
                    count = when(tab) {
                        ETribeTabs.All -> tribeDetail?.totalMembers
                        ETribeTabs.Active -> tribeDetail?.totalActiveMembers
                        ETribeTabs.Inactive -> tribeDetail?.totalInactiveMembers
                    },
                    isSelected = currentSelectedTab == tab,
                    onClick = { viewModel.setTab(tab = tab) }
                )
            }
        }

        CustomVerticalSpacer(space = R.dimen.activity_margin)
        CustomPaginationView(
            lazyPagingItem = eTribeList,
            emptyItemIcon = R.drawable.ic_tribe_empty,
            loadingAnimation = {
                ComposeShimmerListLayout(itemCount = 10) { brush ->
                    ETribeShimmerSingleItem(brush = brush)
                }
            },
            emptyViewTitle = when(currentSelectedTab) {
                ETribeTabs.All -> R.string.e_tribe_no_members_empty_title
                ETribeTabs.Active -> R.string.e_tribe_no_active_members_empty_title
                else -> R.string.e_tribe_no_in_active_members_empty_title
            },
            lazyColumnContent = {
                items(eTribeList.itemCount) {
                    val item = eTribeList[it]
                    val countryFlag = countryFlagList?.get(item?.countryCode)
                    ETribeSingleItem(
                        item = item?.copy(countryFlag = countryFlag),
                        onUserProfileClick = { userId -> onUserProfileClick(userId) }
                    )
                    Divider(
                        modifier = Modifier.padding(top = 15.dp),
                        color = colorResource(id = R.color.colorDivider)
                    )
                }
            }
        )
    }
}
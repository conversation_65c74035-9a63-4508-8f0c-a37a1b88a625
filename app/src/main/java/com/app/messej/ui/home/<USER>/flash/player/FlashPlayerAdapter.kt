package com.app.messej.ui.home.publictab.flash.player

import android.annotation.SuppressLint
import android.media.session.PlaybackState
import android.util.Log
import android.view.GestureDetector
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import androidx.annotation.OptIn
import androidx.core.view.ViewCompat
import androidx.core.view.isGone
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.FlashVideoStats
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.databinding.LayoutFlashPlayerItemBinding
import com.google.android.material.button.MaterialButton

class FlashPlayerAdapter(private val listener: PlayerActionListener) : PagingDataAdapter<FlashPlayerAdapter.FlashPlayerUIModel, FlashPlayerAdapter.FeedsPlayerViewHolder>(FeedsDiff) {

    interface PlayerActionListener {
        fun onLike(pos: Int, item: FlashVideo)
        fun onComment(pos: Int, item: FlashVideo)
        fun onShare(pos: Int, item: FlashVideo)
        fun onMoreOptions(pos: Int, item: FlashVideo, view: MaterialButton, stats: FlashVideoStats)
        fun onClick(pos: Int, item: FlashVideo)
        fun onClickUser(pos: Int, item: FlashVideo)
        fun onGiftClick(item: FlashVideo)
        fun isGiftHidden(item: FlashVideo):Boolean
        fun canPlayFlashVideo(flash: FlashVideo): Boolean
        fun registerForFuturePlayback(obj: FuturePlaybackObject)
        fun preloadMedia(flash: FlashVideo)
        fun detachFuturePlayback(pos: Int)
        fun onLivePodiumIndicatorClicked(flashVideo: FlashVideoStats, item: FlashVideo) {}
//        fun swipeLeft(flash: FlashVideo)
//        fun swipeRight(flash: FlashVideo)
    }

    data class FuturePlaybackObject(
        val pos: Int,
        val flash: FlashVideo,
        val onPlay: (Player) -> Unit,
        val onStop: () -> Unit
    )

    data class FlashPlayerUIModel(
        val flash: FlashVideo,
        val stats: FlashVideoStats,
        @DrawableRes var countryFlag: Int? = null,
    )

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = FeedsPlayerViewHolder(
        LayoutFlashPlayerItemBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
    )

    override fun onBindViewHolder(holder: FeedsPlayerViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    inner class FeedsPlayerViewHolder(private val binding: LayoutFlashPlayerItemBinding) : RecyclerView.ViewHolder(binding.root) {

        private var flashVideo: FlashVideo? = null

        init {
            binding.playerFooter.layoutTransition.setAnimateParentHierarchy(false)
        }

        @OptIn(UnstableApi::class) @SuppressLint("ClickableViewAccessibility")
        fun bind(data: FlashPlayerUIModel) = with(binding) {
            val item = data.flash
            flashVideo = item
            flash = item
            isGiftHidden =listener.isGiftHidden(item)
            stats = data.stats
            Log.d("BVSM1","Inside bind:${flash?.caption} ${flash?.senderDetails?.userLivePodium} ${data.stats.userLivePodium} ${System.identityHashCode(data.stats)}")
            cannotPlayOverlay.isGone = listener.canPlayFlashVideo(item)
            flashCountryFlag.setImageResource(data.countryFlag ?: 0)
//            feedText.apply {
//                maxLines = 1
//                setOnClickListener {
//                    maxLines = if(maxLines==1) Integer.MAX_VALUE else 1
//                }
//            }
            ViewCompat.setTransitionName(playerView, BaseFlashPlayerFragment.transitionNameForId(item.id))
            playerView.apply {
                useController = false
                resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM

                val gestureDetectorCompat = GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {

                    override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
                        if(player?.playbackState != PlaybackState.STATE_PAUSED) {
                            listener.onClick(bindingAdapterPosition, item)
                            binding.isPaused = !(binding.isPaused != null && binding.isPaused == true)
                        }
                        return super.onSingleTapConfirmed(e)
                    }

                })

                setOnTouchListener { _, event ->
                    //To handle Long press
                    gestureDetectorCompat.onTouchEvent(event)
                    when (event.action) {
                        MotionEvent.ACTION_UP -> {
                            true
                        }
                        else -> false
                    }
                }
            }
//            likeButton.setOnClickListener {
//                listener.onLike(bindingAdapterPosition,item)
//            }
            giftButton.setOnClickListener {
                listener.onGiftClick(item)
            }

            playButton.setOnClickListener {
                listener.onClick(bindingAdapterPosition, item)
                binding.isPaused = false
            }
//            giftView.setOnClickListener {
//                if(!listener.isGiftBlocked(item)) {
//                    listener.onGiftClick(item)
//                }
//            }
            commentButton.visibility = if(item.senderDetails?.premium==true) View.VISIBLE else View.GONE
            commentButton.setOnClickListener { listener.onComment(bindingAdapterPosition,item) }
            shareButton.setOnClickListener { listener.onShare(bindingAdapterPosition,item) }
            moreButton.setOnClickListener {
                listener.onMoreOptions(bindingAdapterPosition,item,moreButton,data.stats)
            }
            userDp.setOnClickListener {
                if (data.stats.userLivePodium && data.stats.userLivePodiumId != null) {
                    listener.onLivePodiumIndicatorClicked(data.stats,item)
                }
                else listener.onClickUser(bindingAdapterPosition,item)
            }


//            playerView.setOnTouchListener(object: SwipeGestureListener(mainScreen.rootView.context) {
//                override fun onSwipeRight(): Boolean {
//                    listener.swipeRight(flashVideo?:return false)
//                    return true
//                }
//
//                override fun onSwipeLeft(): Boolean {
//                    listener.swipeLeft(flashVideo?:return false)
//                    return true
//                }
//            })
        }

        fun bindPlayer(): FuturePlaybackObject? = with(binding) {
            flashVideo?.let {
                Log.w("FLASHP", "bindPlayer:$bindingAdapterPosition | ${it.caption}")

                val fpo = FuturePlaybackObject(bindingAdapterPosition, it, { player ->
//                    Log.w("FLASHP", "binding player: ${it.videoUrl}")
                    player.apply {
                        playerView.player = this
                    }
                },{
                    cleanup()
                })
                listener.registerForFuturePlayback(fpo)
                try {
                    peek(bindingAdapterPosition+1)?.let { next ->
                        listener.preloadMedia(next.flash)
                    }
                } catch (_: Exception) {}
                return@with fpo
            }
        }

        fun cleanup() = with(binding) {
            listener.detachFuturePlayback(bindingAdapterPosition)
            playerView.player = null
        }
    }

    override fun onViewDetachedFromWindow(holder: FeedsPlayerViewHolder) {
        super.onViewDetachedFromWindow(holder)
        Log.w("VPLAY", "onViewDetachedFromWindow: $holder")
        holder.cleanup()
    }

    override fun onViewAttachedToWindow(holder: FeedsPlayerViewHolder) {
        super.onViewAttachedToWindow(holder)
        Log.w("VPLAY", "onViewAttachedToWindow: $holder")
        holder.bindPlayer()
    }

    object FeedsDiff : DiffUtil.ItemCallback<FlashPlayerUIModel>() {
        override fun areItemsTheSame(oldItem: FlashPlayerUIModel, newItem: FlashPlayerUIModel): Boolean {
            return oldItem.flash.id == newItem.flash.id
        }

        override fun areContentsTheSame(oldItem: FlashPlayerUIModel, newItem: FlashPlayerUIModel): Boolean {
            return oldItem == newItem
        }
    }
}

package com.app.messej.ui.home.publictab.myETribe

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.eTribe.ETribeResponse.Companion.ADMIN_DELETED
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.databinding.FragmentMyETribeBinding
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class MyETribeFragment : Fragment(), MenuProvider {

    private lateinit var binding: FragmentMyETribeBinding
    private val viewModel : MyETribeViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_my_e_tribe, container,false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolbar.setNavigationOnClickListener {
            findNavController().popBackStack()
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    private fun setup() {
        binding.composeViewMyETribe.setContent {
            ETribeScreen(
                viewModel = viewModel,
                onSuperStarTribeClick = { tribeId ->
                    findNavController().navigateSafe(
                        NavGraphHomeDirections.actionGlobalNavChatHuddle(tribeId)
                    )
                },
                onUserProfileClick = { userId ->
                    findNavController().navigateSafe(
                        NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(userId, false)
                    )
                }
            )
        }
        setupClickListeners()
    }

    private fun setupClickListeners() {
        binding.customActionBar.layoutEditTribeName.setOnClickListener {
            viewModel.setEditTribeAlertBoxVisibility()
        }
    }

    private fun observe() {
        viewModel.tribeDetail.observe(viewLifecycleOwner) { tribe ->
            binding.apply {
                tribeName = tribe?.tribeName
                tribeImage = tribe?.tribeImage
            }
        }

        viewModel.errorMessage.observe(viewLifecycleOwner) { msg ->
            msg?.let { showToast(message = it) }
        }

        viewModel.tribeDetail.observe(viewLifecycleOwner) {
            if (it == null) return@observe
            requireActivity().invalidateOptionsMenu()
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        menuInflater.inflate(R.menu.menu_my_e_tribe, menu)
        viewModel.tribeDetail.value?.tribeStatus?.let {
            menu.findItem(R.id.menu_contact_e_tribe).isVisible = it != ADMIN_DELETED
        }
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when(menuItem.itemId) {
            R.id.menu_contact_e_tribe -> {
                if ((viewModel.tribeDetail.value?.totalMembers ?: 0) == 0) {
                    showToast(message = R.string.e_tribe_no_dear_users_message)
                    return true
                }
                findNavController().navigateSafe(
                    MyETribeFragmentDirections.actionMyETribeFragmentToContactETribeFragment(
                        allUsersCount = viewModel.tribeDetail.value?.totalMembers ?: 0,
                        activeUsersCount = viewModel.tribeDetail.value?.totalActiveMembers ?: 0,
                        inactiveUsersCount = viewModel.tribeDetail.value?.totalInactiveMembers ?: 0,
                    )
                )
            }
            R.id.menu_contact_e_tribe_info -> {
                findNavController().navigateSafe(
                    MyETribeFragmentDirections.actionGlobalPolicyFragment(
                        documentType = DocumentType.E_TRIBE_ABOUT,
                        isButtonVisible = false
                    )
                )
            }
        }
        return true
    }
}
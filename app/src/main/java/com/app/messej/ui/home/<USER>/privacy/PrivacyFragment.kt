package com.app.messej.ui.home.settings.privacy

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.PrivacySettingsMode
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.FragmentPrivacySettingsBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PrivacyFragment : Fragment() {

    private val viewModel: PrivacyViewModel by viewModels()
    lateinit var binding: FragmentPrivacySettingsBinding

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_privacy_settings, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observe()
        setup()
    }

    private fun setup() {

        binding.privateSettingLastSeen.title =getString(R.string.title_privacy_setting_last_seen)
        binding.privateSettingLastSeen.subTitle =getString(R.string.subtitle_privacy_setting_last_seen)

        binding.privateSettingOnlineStatus.title =getString(R.string.title_privacy_setting_online_status)
        binding.privateSettingOnlineStatus.subTitle =getString(R.string.subtitle_privacy_setting_online_status)

        binding.privateSettingProfilePhoto.title =getString(R.string.title_privacy_setting_profile_photo)
        binding.privateSettingProfilePhoto.subTitle =getString(R.string.subtitle_privacy_setting_profile_photo)

        binding.privateSettingLastAbout.title=getString(R.string.title_privacy_setting_about)
        binding.privateSettingLastAbout.subTitle=getString(R.string.subtitle_privacy_setting_about)

        binding.privateSettingBlockedStars.title=getString(R.string.title_privacy_setting_bocked_stars)
        binding.privateSettingBlockedStars.subTitle=getString(R.string.subtitle_privacy_setting_blocked_stars)
        binding.privateSettingBlockedStars.root.setOnClickListener {
            onBlockedStarsClick()
        }

        binding.privateSettingBroadcastPrivacy.title=getString(R.string.title_privacy_setting_broadcast_privacy)
        binding.privateSettingBroadcastPrivacy.subTitle=getString(R.string.subtitle_privacy_setting_broadcast_privacy)
        binding.privateSettingBroadcastPrivacy.root.setOnClickListener {
            onBlockBroadCastClick()
        }

        binding.privateSettingMessagePrivacy.title=getString(R.string.title_privacy_setting_message_privacy)
        binding.privateSettingMessagePrivacy.subTitle=getString(R.string.subtitle_privacy_setting_message_privacy)
        binding.privateSettingMessagePrivacy.root.setOnClickListener {
            onPrivateMessagePrivacy()
        }


        binding.privateSettingBlockedMessages.title=getString(R.string.title_privacy_setting_message_blocked_messages)
        binding.privateSettingBlockedMessages.subTitle=getString(R.string.subtitle_privacy_setting_message_blocked_message)
        binding.privateSettingBlockedMessages.root.setOnClickListener{onBlockedPrivateMessagesClick()}

        binding.privateSettingBlockedHuddles.title=getString(R.string.title_privacy_setting_message_blocked_huddles)
        binding.privateSettingBlockedHuddles.subTitle=getString(R.string.subtitle_privacy_setting_message_blocked_huddles)
        binding.privateSettingBlockedHuddles.root.setOnClickListener{onBlockedHuddlesClick()}

        binding.countryFlag.title = getString(R.string.title_country_flag)
        binding.countryFlag.subTitle=getString(R.string.subtitle_coutry_settings)

        binding.countryFlag.actionSwitch.setOnCheckedChangeListener{_, isChecked ->
            if (viewModel.hasToDisplayCountry.value != isChecked)
                viewModel.handleCountryFlagUpdate(isChecked)
        }
        binding.privateSettingPodiumPrivacy.title= getString(R.string.title_podium_privacy)
        binding.privateSettingPodiumPrivacy.subTitle = getString(R.string.sub_title_podium_privacy_options)
        binding.privateSettingPodiumPrivacy.root.setOnClickListener {
            onPodiumPrivacyClick()
        }
    }

    private fun onPodiumPrivacyClick() {
        findNavController().navigateSafe(PrivacyFragmentDirections.actionPrivacyFragmentToPodiumPrivacyFragment())
    }

    private fun onBlockBroadCastClick() {
       findNavController().navigateSafe(PrivacyFragmentDirections.actionPrivacyFragmentToBlockedBroadcast())
    }

    private fun onBlockedStarsClick() {
        findNavController().navigateSafe(PrivacyFragmentDirections.actionPrivacyFragmentToBlockedStars())
    }

    private fun observe() {

        viewModel.aboutPrivacy.observe(viewLifecycleOwner){it->
            it?.let {privacy->
                binding.privateSettingLastAbout.helpText = getPrivacyHelpText(it.privacy)
                binding.privateSettingLastAbout.root.setOnClickListener {
                    privacy.privacy?.let { it -> onAboutClick(it, privacy.audience) }
                }
            }
        }

        viewModel.userAccount.observe(viewLifecycleOwner){
            it?.let {
                binding.apply {

                    if (it.citizenship == UserCitizenship.MINISTER || it.citizenship == UserCitizenship.PRESIDENT) {
                        privateSettingOnlineStatus.itemSettingsLayout.visibility = View.VISIBLE
                    }
                    else {
                        privateSettingOnlineStatus.itemSettingsLayout.visibility = View.GONE
                    }

                    if (it.profile.premium) {
                        privateSettingLastSeen.itemSettingsLayout.visibility = View.VISIBLE
                        privateSettingLastAbout.itemSettingsLayout.visibility = View.VISIBLE
                        privateSettingProfilePhoto.itemSettingsLayout.visibility = View.VISIBLE

                    } else {
                        countryFlag.notificationSettingsLayout.visibility = View.GONE
                        privateSettingLastSeen.itemSettingsLayout.visibility = View.GONE
                        privateSettingLastAbout.itemSettingsLayout.visibility = View.GONE
                        privateSettingProfilePhoto.itemSettingsLayout.visibility = View.GONE
                    }
                }

            }
        }

        viewModel.lastSeenPrivacy.observe(viewLifecycleOwner){

            it?.let { lastSeenPrivacy->
                binding.privateSettingLastSeen.helpText = getPrivacyHelpText(it.privacy)
                binding.privateSettingLastSeen.root.setOnClickListener {
                    lastSeenPrivacy.privacy?.let { privacy ->
                        onLastSeenClick(privacy, lastSeenPrivacy.audience) }
                }
            }
            }

        viewModel.onlineStatusPrivacy.observe(viewLifecycleOwner){ it->
            it?.let { onlineStatusPrivacy->
                binding.privateSettingOnlineStatus.helpText = getPrivacyHelpText(it.privacy)
                binding.privateSettingOnlineStatus.root.setOnClickListener {
                    onlineStatusPrivacy.privacy?.let { privacy -> onOnlineStatusClick(privacy, onlineStatusPrivacy.audience) }
                }
            }
        }

        viewModel.profilePhotoPrivacy.observe(viewLifecycleOwner){it->
            it?.let {  profilePhotoPrivacy->
                binding.privateSettingProfilePhoto.helpText = getPrivacyHelpText(it.privacy)
                binding.privateSettingProfilePhoto.root.setOnClickListener {
                    profilePhotoPrivacy.privacy?.let { privacy -> onProfileClick(privacy, profilePhotoPrivacy.audience) }
                }
            }

        }
        viewModel.privacyApiFailedMessage.observe(viewLifecycleOwner){
            it?.let {
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.onCountryFlagUpdated.observe(viewLifecycleOwner){
            if (it && viewModel.userAccount.value?.userEmpowerment?.canHideFlag == true)
                showToast(R.string.country_flag_shown_message)
            else showToast(R.string.country_flag_not_shown_message)
        }
    }

    override fun onStart() {
        super.onStart()
       (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.settings_title_privacy)
        viewModel.getPrivacyDetails()
    }

    //on online status click
    private fun onOnlineStatusClick(privacy: String, audience: List<String>) {
        findNavController().navigateSafe(PrivacyFragmentDirections.actionPrivacyFragmentToPrivacySelectionFragment(PrivacySettingsMode.ONLINE_STATUS, privacy, audience.toTypedArray()))
    }

    private fun onAboutClick(privacy: String, audience: List<String>) {
        findNavController().navigateSafe(PrivacyFragmentDirections.actionPrivacyFragmentToPrivacySelectionFragment(PrivacySettingsMode.ABOUT, privacy, audience.toTypedArray()))
    }
    private fun onProfileClick(privacy: String, audience: List<String>) {
        findNavController().navigateSafe(PrivacyFragmentDirections.actionPrivacyFragmentToPrivacySelectionFragment(PrivacySettingsMode.PROFILE_PHOTO, privacy, audience.toTypedArray()))
    }

    private fun onBlockedHuddlesClick() {
        findNavController().navigateSafe(PrivacyFragmentDirections.actionPrivacyFragmentToBlockedHuddlesFragment())
    }

    private fun onBlockedPrivateMessagesClick() {
        findNavController().navigateSafe(PrivacyFragmentDirections.actionPrivacyFragmentToBlockedPrivateMessagesFragment())
    }
    private fun onLastSeenClick(privacy: String, audience: List<String>) {

       findNavController().navigateSafe(PrivacyFragmentDirections.actionPrivacyFragmentToPrivacySelectionFragment(PrivacySettingsMode.LAST_SEEN,privacy,audience.toTypedArray()))

    }

    private fun onPrivateMessagePrivacy() {
   findNavController().navigateSafe(PrivacyFragmentDirections.actionPrivacyFragmentToPrivateMessagesPrivacyFragment())
    }
    private fun getPrivacyHelpText(privacy: String?): String {
        return when (privacy) {
            "Everyone" -> resources.getString(R.string.title_settings_everyone)
            "Nobody" -> resources.getString(R.string.title_settings_nobody)
            else -> resources.getString(R.string.title_settings_custom)
        }
    }


}
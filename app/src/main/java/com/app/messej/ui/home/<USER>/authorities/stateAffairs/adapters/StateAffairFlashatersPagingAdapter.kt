package com.app.messej.ui.home.publictab.authorities.stateAffairs.adapters

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.enums.StateAffairsTypes
import com.app.messej.databinding.ItemAffairUserLayoutBinding
import com.app.messej.ui.home.publictab.authorities.stateAffairs.UserStateAffair
import com.app.messej.ui.utils.BindingExtensions.bindFlashatersData

class StateAffairFlashatersPagingAdapter(
    private val isLargeScreen: Boolean? = false,
    private val listType: StateAffairsTypes,
    private val listener: stateAffairActionListener
) : PagingDataAdapter<UserStateAffair, UserViewHolder>(TransactionsDiffer) {

    interface stateAffairActionListener{
        fun onProfileClick(item: UserStateAffair)
    }

    override fun onBindViewHolder(holder: UserViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it, isLargeScreen, listType,listener) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UserViewHolder {
        val binding = ItemAffairUserLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return UserViewHolder(binding)
        }
    }


    class UserViewHolder(private val binding: ItemAffairUserLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: UserStateAffair, isLargeScreen: Boolean?, listType: StateAffairsTypes, listener: StateAffairFlashatersPagingAdapter.stateAffairActionListener) {
            binding.stateAffair = item
            binding.bindFlashatersData(item, isLargeScreen, listType)

            // Add click listener to the entire card
            binding.cardLayout.setOnClickListener {
                listener.onProfileClick(item)
//                Log.i("SA", "bind: clicked profile")
            }
        }
    }



object TransactionsDiffer : DiffUtil.ItemCallback<UserStateAffair>() {
    override fun areItemsTheSame(oldItem: UserStateAffair, newItem: UserStateAffair) = oldItem.userId == newItem.userId

    override fun areContentsTheSame(oldItem: UserStateAffair, newItem: UserStateAffair) = oldItem == newItem
}


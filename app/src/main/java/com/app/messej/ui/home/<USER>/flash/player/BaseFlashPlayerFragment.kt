package com.app.messej.ui.home.publictab.flash.player

import android.animation.ValueAnimator
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import androidx.annotation.OptIn
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.PopupMenu
import androidx.core.animation.addListener
import androidx.core.content.ContextCompat
import androidx.core.view.doOnPreDraw
import androidx.core.view.get
import androidx.core.view.isVisible
import androidx.databinding.BindingAdapter
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import androidx.navigation.fragment.FragmentNavigator
import androidx.navigation.fragment.FragmentNavigatorExtras
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.app.messej.NavGraphHomeDirections
import com.app.messej.NavMyFlashDirections
import com.app.messej.R
import com.app.messej.data.model.FlashVideoStats
import com.app.messej.data.model.ReportPackage
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.ReportType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.FragmentFlashPlayerBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureFlashPostingAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportBanAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportContentAllowed
import com.app.messej.ui.home.gift.GiftListBottomSheetFragment
import com.app.messej.ui.home.publictab.flash.PublicFlashPlayerFragmentDirections
import com.app.messej.ui.home.publictab.flash.player.cache.FlashVideoPreloadManager
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.validateAndConfirmJoinFromGreenDot
import com.app.messej.ui.legal.report.ReportFragment.Companion.setReportListener
import com.app.messej.ui.legal.report.ReportUtils.canBan
import com.app.messej.ui.legal.report.ReportUtils.canReport
import com.app.messej.ui.legal.report.ReportUtils.canReportAndHide
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.setupMenuItemTextColor
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.Synchronize
import com.app.messej.ui.utils.ViewUtils
import com.bumptech.glide.Glide
import com.google.android.material.button.MaterialButton
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import com.kennyc.view.MultiStateView
import jp.wasabeef.glide.transformations.BlurTransformation
import jp.wasabeef.glide.transformations.GrayscaleTransformation
import jp.wasabeef.transformers.glide.ColorFilterTransformation
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference
import kotlin.math.roundToInt

abstract class BaseFlashPlayerFragment : Fragment(), FlashPlayerAdapter.PlayerActionListener {

    protected var mAdapter: FlashPlayerAdapter? = null

    protected abstract val viewModel: BaseFlashPlayerViewModel

    protected lateinit var binding: FragmentFlashPlayerBinding

    companion object {
        fun transitionNameForId(id: String): String {
            return "flash_transition_$id"
        }

        fun getTransitionExtras(view: View, id: String): FragmentNavigator.Extras {
            return FragmentNavigatorExtras(view to transitionNameForId(id))
        }

        fun getBlankTransitionExtras(): FragmentNavigator.Extras {
            return FragmentNavigatorExtras()
        }

        @JvmStatic
        @BindingAdapter("imageUrl", "flashBlocked")
        fun loadImage(view: AppCompatImageView, url: String?, blocked: Boolean?) { // This methods should not have any return type, = declaration would make it return that object declaration.
            if (blocked==true) {
                Glide.with(view.context).load(url).transform(
                    BlurTransformation(25),
                    GrayscaleTransformation(),
                    ColorFilterTransformation(Color.argb(0.6f, 0f, 0f, 0f))
                ).into(view)
            } else {
                Glide.with(view.context).load(url).into(view)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_player, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        postponeEnterTransition()
        super.onViewCreated(view, savedInstanceState)
//        addAsMenuHost()
        setup()
        observe()
    }

    protected open fun setup() {

        setupPlayer()
        initAdapter()

        binding.closeButton.setOnClickListener {
            findNavController().popBackStack()
        }

        setReportListener { type, success ->
            if (type!= ReportContentType.FLASH || !success) return@setReportListener
            mAdapter?.refresh()
            findNavController().popBackStack()
        }

        setFragmentResultListener(GiftListBottomSheetFragment.GIFT_REQUEST_KEY) { _, bundle ->
            val flashId = bundle.getString(GiftListBottomSheetFragment.CONTEXT_ID)
            Log.d("BVSM","listner inside $flashId")
            flashId?.let {
                viewModel.getDetailForItem(it)
            }
        }
    }

    private fun observe() {
        viewModel.flashPlayerFeedList.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let {
                mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
            }
            (view?.parent as? ViewGroup)?.doOnPreDraw {
                startPostponedEnterTransition()
            }
        }
        viewModel.onFlashSaved.observe(viewLifecycleOwner) {
            showSnackbar(R.string.flash_saved_toast,Snackbar.LENGTH_SHORT)
        }
        viewModel.onFlashBlocked.observe(viewLifecycleOwner) {
            showSnackbar(R.string.flash_blocked_toast)
        }

        viewModel.onCommentDisabled.observe(viewLifecycleOwner) {
            showSnackbar(if (!it)  R.string.flash_enable_comment_toast else R.string.flash_disable_comment_toast)
        }
        viewModel.debouncedViewState.observe(viewLifecycleOwner) {
            binding.loader.isVisible = (it == MultiStateView.ViewState.LOADING)
        }
        viewModel.onFlashDeleted.observe(viewLifecycleOwner) {
            showToast(R.string.flash_action_delete_toast)
            mAdapter?.refresh()
            findNavController().popBackStack()
        }
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = FlashPlayerAdapter(this) .apply {
            addOnPagesUpdatedListener {
                viewModel.activeItem?.let { active ->
                    Log.w("FLASHP", "active item $active | current: ${binding.flashViewPager.currentItem} | items: ${this.itemCount}")
                    if (active != binding.flashViewPager.currentItem) {
                        binding.flashViewPager.setCurrentItem(active, false)
                    }
                }
            }
        }

        binding.flashViewPager.apply {
            adapter = mAdapter
            registerOnPageChangeCallback(object: ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    viewModel.setActiveItem(position,true)
                    Log.w("FLASHP", "onPageSelected: $position")
                    futurePlaybackObject?.get().let { fpo ->
                        if (fpo!=null) {
                            if (fpo.pos != position) {
                                Log.w("FLASHP", "onPageSelected: stopping ${fpo.pos}")
                                stopPlayer()
                                fpo.onStop.invoke()
                                futurePlaybackObject = null
                            }
                        } else stopPlayer()
                    }
                    playMediaIfReady()
                }
            })
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                if (state==MultiStateView.ViewState.EMPTY) popSafely()
                viewModel.setViewState(state)
            }
        }
    }

    private var didPopBackstack: Boolean by Synchronize(false)

    private fun popSafely() {
        Log.w("PLF", "popSafely: didPopBackstack: $didPopBackstack")
        if (didPopBackstack) return
        didPopBackstack = true
        Log.w("PLF", "popSafely: popping...")
        findNavController().popBackStack()
    }

    private fun showMoreMenu(view: MaterialButton, flash: FlashVideo, stats: FlashVideoStats) {
        val popup = PopupMenu(requireContext(),view)
        popup.menuInflater.inflate(R.menu.menu_flash_player_action_more, popup.menu)
        popup.setForceShowIcon(true)

        Log.w("BFPF", "showMoreMenu: $flash")
        Log.w("BFPF", "showMoreMenu: ${stats.citizenship}")

        val iAmPremiumUser = viewModel.user.premium
        val isMyOwnFlash = flash.userId==viewModel.user.id
        val isReported = flash.isReported
        val iAmMinister = viewModel.isMinister
        val iAmPresident = viewModel.isPresident
//        val isUserBlocked = flash.senderDetails?.userflashBlocked == true
        val isUserBlocked = stats.userBlockedFromFlash
        val isPresidentFlash =  stats.citizenship == UserCitizenship.PRESIDENT

        popup.menu.apply {
//            findItem(R.id.action_save).isVisible = !isMyOwnFlashT

            findItem(R.id.action_block).isVisible = (!isMyOwnFlash && !isUserBlocked && !isPresidentFlash) && (viewModel.hasEmpowermentToBlockUserOnFlash || iAmMinister|| iAmPresident)
            findItem(R.id.action_delete).isVisible = isMyOwnFlash || viewModel.canDeleteFlashVideo==true || iAmPresident || iAmMinister
            findItem(R.id.action_comment_toggle).apply {
                isVisible = isMyOwnFlash && iAmPremiumUser
                icon = ContextCompat.getDrawable(requireContext(),if(!flash.commentDisabled) R.drawable.ic_comment_disable else R.drawable.ic_comment)
                title = resources.getString(if (!flash.commentDisabled)  R.string.flash_disable_comment else R.string.flash_enable_comment )
            }
            findItem(R.id.action_report).apply {
                isVisible = !isMyOwnFlash && !isReported && viewModel.user.canReport(stats.citizenship)
                setupMenuItemTextColor(
                    color = R.color.colorError,
                    context = requireContext(),
                    isTextBold = true
                )
            }
            findItem(R.id.action_report_and_hide).apply {
                isVisible = !isMyOwnFlash && viewModel.user.canReportAndHide(stats.citizenship)
                setupMenuItemTextColor(
                    color = R.color.colorError,
                    context = requireContext(),
                    isTextBold = true
                )
            }
            findItem(R.id.action_ban).apply {
                isVisible = !isMyOwnFlash && viewModel.user.canBan(stats.citizenship)
                setupMenuItemTextColor(
                    color = R.color.colorError,
                    context = requireContext(),
                    isTextBold = true
                )
            }
        }
        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_save -> ensureFlashPostingAllowed {
                    if (isMyOwnFlash) {
                        viewModel.saveToGallery(flash)
                        showToast(R.string.video_start_download_message)
                    } else {
                        viewModel.saveFlash(flash)
                    }
                }
                R.id.action_delete -> ensureFlashPostingAllowed {
                    showRecordingDeleteAlert {
                        viewModel.deleteFlash(flash)
                    }
                }
                R.id.action_comment_toggle -> viewModel.commentToggle(flash)
                R.id.action_block -> confirmBlockFlashUser(flash)
                R.id.action_report-> {
                    ensureReportContentAllowed {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.Flash(flash, reportType = ReportType.REPORT).serialize()))
                    }
                }
                R.id.action_report_and_hide-> {
                    ensureReportContentAllowed {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.Flash(flash, reportType = ReportType.REPORT_AND_HIDE).serialize()))
                    }
                }
                R.id.action_ban-> {
                    ensureReportBanAllowed {
                        flash.senderDetails?.asBasicUser()?.let {
                            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.User(it, reportType = ReportType.BAN).serialize()))
                        }
                    }
                }
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    override fun onLike(pos: Int, item: FlashVideo) {
        viewModel.toggleLike(item, pos)
    }
    override fun onComment(pos: Int, item: FlashVideo) {
        findNavController().navigateSafe(NavMyFlashDirections.actionGlobalNavFlashComment(item.id,item.userId,!item.commentDisabled))
    }
    override fun onShare(pos: Int, item: FlashVideo) {
        item.shareLink?.let {
            shareVideoLink(it)
        }
    }

    private fun shareVideoLink(shareLink: String) {
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(
                Intent.EXTRA_TEXT, getString(R.string.flash_share_video_url_text, shareLink)
            )
            type = "text/plain"
        }
        val shareIntent = Intent.createChooser(sendIntent, null)
        startActivity(shareIntent)
    }

    override fun onMoreOptions(pos: Int, item: FlashVideo, view: MaterialButton, stats: FlashVideoStats) {
        showMoreMenu(view,item,stats)
    }

//    override fun swipeLeft(flash: FlashVideo) {
//        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavFlashUser(viewModel.user.id))
//    }
//
//    override fun swipeRight(flash: FlashVideo) {
//        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavFlashUser(flash.userId))
//    }

    override fun onClick(pos: Int, item: FlashVideo) {
        player?.apply {
            if (isPlaying) {
                pause()
            } else {
                play()
                startProgressPolling()
            }
        }
    }

    override fun onResume() {
        super.onResume()
//        Log.d("FLASHP", "onResume: ")
        playMediaIfReady()
    }

    override fun onPause() {
        super.onPause()
        stopPlayer()
    }
    private var futurePlaybackObject: WeakReference<FlashPlayerAdapter.FuturePlaybackObject>? = null

    override fun registerForFuturePlayback(obj: FlashPlayerAdapter.FuturePlaybackObject) {
        if (futurePlaybackObject?.get()?.pos==obj.pos) return
        futurePlaybackObject = WeakReference(obj)
        Log.d("FLASHP", "registerForFuturePlayback: ${obj.pos}")
        if (obj.pos==viewModel.activeItem) playMediaIfReady()
    }

    override fun detachFuturePlayback(pos: Int) {
        Log.d("FLASHP", "detachFuturePlayback: $pos | current - ${futurePlaybackObject?.get()?.pos}")
        if (futurePlaybackObject?.get()?.pos == pos) {
            stopPlayer()
            futurePlaybackObject = null
        }
    }

    @OptIn(UnstableApi::class)
    override fun preloadMedia(flash: FlashVideo) {
        viewModel.preloadMedia(flash)
    }

    override fun canPlayFlashVideo(flash: FlashVideo): Boolean = true

    private fun playMediaIfReady() {
        val videoPlayer = player?: return
        fun play(fpo: FlashPlayerAdapter.FuturePlaybackObject) {
            videoPlayer.setupPlayerWithMedia(fpo.flash)
            fpo.onPlay.invoke(videoPlayer)
            viewModel.getDetailForItem(fpo.flash.id)
        }
        var fpo = futurePlaybackObject?.get()
        if (fpo==null || fpo.pos!=viewModel.activeItem) {
            Log.d("FLASHP", "playMediaIfReady: trying to find current FPO")
            val vh = (binding.flashViewPager[0] as RecyclerView).findViewHolderForAdapterPosition(binding.flashViewPager.currentItem)
//                Log.w("FLASHP", "playMediaIfReady: found ViewHolder for pos ${binding.mediaViewPager.currentItem}")
            fpo = (vh as? FlashPlayerAdapter.FeedsPlayerViewHolder)?.bindPlayer()
            Log.d("FLASHP", "fpo is $fpo")
        }
        fpo?: return
        Log.w("FLASHP", "playMediaIfReady: found FPO: ${fpo.pos}")
        if (canPlayFlashVideo(fpo.flash)) {
            play(fpo)
        } else {
            stopPlayer()
//            val pos = viewModel.skipToNext()?: return
//            binding.flashViewPager.setCurrentItem(pos, false)
        }
    }

    private fun stopMediaPlayback() {
        futurePlaybackObject?.get()?.let { fpo ->
            Log.d("FLASHP", "stopMediaPlayback: found FPO: ${fpo.pos}")
            fpo.onStop.invoke()
        }
    }

    private var player: ExoPlayer? = null

    private fun releasePlayer() {
        stopMediaPlayback()
        futurePlaybackObject = null
        player?.apply {
            stop()
            release()
            player = null
        }
    }

    @OptIn(UnstableApi::class)
    private fun setupPlayer() {
        if (player == null) {
            player = ExoPlayer.Builder(requireContext()).build()
        }
        player?.apply {
            repeatMode = Player.REPEAT_MODE_ONE
            playWhenReady = false
            prepare()
        }
    }

    private fun stopPlayer() {
        player?.stop()
        cancelProgressPolling()
    }

    @OptIn(UnstableApi::class)
    private fun ExoPlayer.setupPlayerWithMedia(flash: FlashVideo) {
        val cookie = viewModel.cookies.value?:return

        val dataSourceFactory = FlashVideoPreloadManager.getCacheDataSource(cookie)

        val mediaSource = ProgressiveMediaSource.Factory(dataSourceFactory)
            .createMediaSource(MediaItem.fromUri(flash.videoUrl))
        Log.w("VPLAY", "setupPlayerWithMedia: ${cookie.cookieValue}")
        Log.w("VPLAY", "setupPlayerWithMedia: ${flash.videoUrl}")
        setMediaSource(mediaSource)
        prepare()
        play()
        startProgressPolling()
    }

    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }

    private var animator: ValueAnimator? = null

    private fun cancelProgressPolling() {
        animator?.let {
            it.cancel()
            animator = null
            binding.videoProgress.progress = 0
        }
    }
    private fun startProgressPolling() {
        cancelProgressPolling()
        animator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = 5000
            interpolator = LinearInterpolator()
            addUpdateListener { _ ->
                try {
                    val current = player?.currentPosition ?: return@addUpdateListener
                    val dur = player?.duration ?: return@addUpdateListener
                    binding.videoProgress.progress = (current.toFloat() / dur * 1000).roundToInt()
                } finally { }
            }
            addListener(onEnd = {
                Log.w("FREF", "checking if still playing ${player?.isPlaying}")
                if (player?.isPlaying==true) {
                    startProgressPolling()
                }
            })
            start()
        }
    }

    override fun onClickUser(pos: Int, item: FlashVideo) {
        if (!viewModel.isSelf(item.userId)) {
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavFlashUser(item.userId))
        }
    }

    private fun showRecordingDeleteAlert(onConfirm: () -> Unit) {
        MaterialAlertDialogBuilder(requireContext()).setTitle(R.string.flash_my_flash_delete_confirm_title)
            .setMessage(getText(R.string.flash_my_flash_delete_confirm_message))
            .setPositiveButton(getText(R.string.common_confirm)) { dialog, _ ->
                dialog.dismiss()
                onConfirm.invoke()
            }.setNegativeButton(getText(R.string.common_cancel)) { dialog, _ ->
                dialog.dismiss()
            }.show()
    }

    override fun onGiftClick(item: FlashVideo) {
        if (viewModel.isCitizenUser || item.senderDetails?.citizenship?.isGolden==true) return
        if(item.senderDetails?.id==viewModel.user.id){
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalGiftFileFragment())
        }else {
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalGiftFragment(receiverId = item.userId, giftContext = GiftContext.GIFT_FLASH, giftContextId = item.id))
        }
    }

    override fun isGiftHidden(item: FlashVideo): Boolean {
//        return false
        return (item.senderDetails?.citizenship?.isVisitor == true||item.senderDetails?.citizenship?.isGolden ==true)
    /*item.senderDetails?.citizenship == UserCitizenship.VISITOR ||*/ /*viewModel.user.citizenship == UserCitizenship.VISITOR*//*||item.senderDetails?.id==viewModel.user.id*/
    }

    protected  fun gotoFlashComments(flashId:String){
        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavFlashComment(flashId,viewModel.user.id))
    }

    private fun confirmBlockFlashUser(flash: FlashVideo) {
        confirmAction(
            message = R.string.flash_block_user_confirm_message,
            positiveTitle = R.string.common_confirm,
            negativeTitle = R.string.common_cancel
        ) {
            viewModel.blockFlashUser(flash.senderDetails?.id?: return@confirmAction)
        }
    }

    override fun onLivePodiumIndicatorClicked(flashVideo: FlashVideoStats, item: FlashVideo) {
        val podiumId = flashVideo.userLivePodiumId ?: return
        lifecycleScope.launch {
            // Use the validateAndConfirmJoinFromGreenDot extension to handle validation and confirmation
            validateAndConfirmJoinFromGreenDot(podiumId, viewModel.user) {
                // Navigate to the podium only after validation passes and user confirms
                findNavController().navigateSafe(
                    PublicFlashPlayerFragmentDirections.actionGlobalNavLivePodium(podiumId)
                )
            }
        }
    }
}
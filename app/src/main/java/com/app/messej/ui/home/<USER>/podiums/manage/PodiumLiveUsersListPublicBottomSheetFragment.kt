package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.MutableLiveData
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.data.model.entity.Podium
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PodiumLiveUsersListPublicBottomSheetFragment : PodiumLiveUsersListBaseFragment() {

    private val viewModel: PodiumLiveUsersListPublicViewModel by viewModels()
    val args: PodiumLiveUsersListPublicBottomSheetFragmentArgs by navArgs()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.setPodiumId(args.podiumId)

        observe()
    }

    override fun refreshData() {
        viewModel.getPodiumDetails(args.podiumId)
    }

    fun observe() {
        viewModel.liveUsersList.observe(viewLifecycleOwner){pagingData ->
            pagingData?.let {
                mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
            }
        }



        viewModel._speakerList.observe(viewLifecycleOwner){

        }

        viewModel.podiumDetails.observe(viewLifecycleOwner){

        }

        loadingStateLiveData.observe(viewLifecycleOwner){
            binding.actionLoading.isVisible=it
        }

        viewModel.onFollowedUser.observe(viewLifecycleOwner){
            mAdapter?.refresh()
            Toast.makeText(requireContext(), getString(R.string.public_star_following_text, it), Toast.LENGTH_SHORT).show()
        }

        viewModel.onUnfollowedUser.observe(viewLifecycleOwner){
            mAdapter?.refresh()
            Toast.makeText(requireContext(), getString(R.string.public_star_unfollowing_text, it), Toast.LENGTH_SHORT).show()
        }

    }

    override val loadingStateLiveData: MutableLiveData<Boolean>
        get() = viewModel.liveUsersListLoading

    override val actionListener: PodiumLiveUsersListAdapter.PodiumActionListener
        get() = object : PodiumLiveUsersListAdapter.PodiumActionListener {
            override fun onMenuClick(view: View, item: PodiumParticipant) { }
            override fun canInviteUser(item: PodiumParticipant): Boolean {
                return false
            }
            override fun isSpeaker(item: PodiumParticipant): Boolean {
                return viewModel.isSpeaker(item.id) && item.role != Podium.PodiumUserRole.MANAGER
            }
            override fun shouldShowFollowButton(item: PodiumParticipant): Boolean {
                val isMySuperstar = viewModel.user.superStarId == item.id
                return viewModel.user.id != item.id && !isMySuperstar
            }

            override fun onFollowClick(item: PodiumParticipant) {
                viewModel.toggleFollow(item.id, item.name, item.isFollowed == true)
            }

            override fun navigateToIdCard(item: PodiumParticipant) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(item.id))
            }
            override fun isNotSelf(item: PodiumParticipant): Boolean {
                return viewModel.user.id != item.id
            }

            override fun isInvited(item: PodiumParticipant): Boolean {
                return false
            }
        }
}
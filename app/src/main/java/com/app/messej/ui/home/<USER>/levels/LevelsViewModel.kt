package com.app.messej.ui.home.settings.levels

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.R
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.AccountRepository
import kotlinx.coroutines.Dispatchers

class LevelsViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo: AccountRepository = AccountRepository(application)

    private val _accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails

    val user: CurrentUser get() = accountRepo.user

    private val _isAlertDialogVisible = MutableLiveData(false)
    val isAlertDialogVisible: LiveData<Boolean> = _isAlertDialogVisible

    val citizenShipAndDescription = listOf<Pair<UserCitizenship, Int>>(
        Pair(UserCitizenship.CITIZEN, R.string.level_citizen_progress_description),
        Pair(UserCitizenship.OFFICER, R.string.level_officer_progress_description),
        Pair(UserCitizenship.AMBASSADOR, R.string.level_ambassador_progress_description),
        Pair(UserCitizenship.MINISTER, R.string.level_minister_progress_description),
    )

    val citizenShipAndDescriptionPresidentAndGolden = listOf<Pair<UserCitizenship, Int>>(
//        Pair(UserCitizenship.GOLDEN, R.string.level_golden_progress_description),
        Pair(UserCitizenship.PRESIDENT, R.string.level_president_progress_description)
    )


    fun setAlertDialogVisibility(isVisible: Boolean) {
        _isAlertDialogVisible.postValue(isVisible)
    }

}
package com.app.messej.ui.home.publictab.podiums.live

import android.app.Application
import android.util.Log
import android.view.SurfaceView
import android.widget.Toast
import androidx.annotation.DrawableRes
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.BuildConfig
import com.app.messej.ConnectivityObserver
import com.app.messej.NetworkConnectivityObserver
import com.app.messej.data.Constants
import com.app.messej.data.agora.AgoraEngineService
import com.app.messej.data.agora.PodiumEventService
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.PaidLikePayload
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.UserGiftPaused
import com.app.messej.data.model.UserRatingProvider
import com.app.messej.data.model.api.PodiumJoinErrorResponse
import com.app.messej.data.model.api.podium.FlagChallengeQuestion
import com.app.messej.data.model.api.podium.HideLiveUsersRequest
import com.app.messej.data.model.api.podium.PodiumChallengeSetupEvent
import com.app.messej.data.model.api.podium.PodiumJoinResponse
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.api.podium.UserStats
import com.app.messej.data.model.api.podium.challenges.ChallengePlayer
import com.app.messej.data.model.api.podium.challenges.KnowledgeRaceData
import com.app.messej.data.model.api.podium.challenges.PenaltyData
import com.app.messej.data.model.api.podium.challenges.PenaltyKickTarget
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.entity.Podium.MaidanStatus.Companion.isChallengeTime
import com.app.messej.data.model.entity.Podium.PodiumUserRole.Companion.orDefault
import com.app.messej.data.model.enums.AcceptDecline
import com.app.messej.data.model.enums.AppLocale
import com.app.messej.data.model.enums.AssemblySpeakingSkipReason
import com.app.messej.data.model.enums.AssemblySpeakingSkippedBy
import com.app.messej.data.model.enums.AssemblySpeakingStatus
import com.app.messej.data.model.enums.AssemblyStopSpeakingReason
import com.app.messej.data.model.enums.BlockUnblockAction
import com.app.messej.data.model.enums.ChallengeContributionType
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.data.model.enums.ConFourGameStatus
import com.app.messej.data.model.enums.FollowerType
import com.app.messej.data.model.enums.GiftType
import com.app.messej.data.model.enums.MaidanLike
import com.app.messej.data.model.enums.PodiumActionType
import com.app.messej.data.model.enums.PodiumBlockFrom
import com.app.messej.data.model.enums.PodiumClosedBy
import com.app.messej.data.model.enums.PodiumEntry
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.PodiumKind.Companion.orDefault
import com.app.messej.data.model.enums.PodiumLiveChatType
import com.app.messej.data.model.enums.PodiumPauseGift
import com.app.messej.data.model.enums.PodiumWhoCanJoin
import com.app.messej.data.model.enums.StarType
import com.app.messej.data.model.enums.TheaterJoinType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserCitizenship.Companion.orDefault
import com.app.messej.data.model.socket.BoxChallengeLineDrawPayload
import com.app.messej.data.model.socket.BoxChallengeLineDrawnPayload
import com.app.messej.data.model.socket.ChallengeGameOverPayload
import com.app.messej.data.model.socket.ChallengeScoreUpdatePayload
import com.app.messej.data.model.socket.ConFourDropTimedOutPayload
import com.app.messej.data.model.socket.ConFourTokenDropPayload
import com.app.messej.data.model.socket.ConFourTokenDroppedPayload
import com.app.messej.data.model.socket.KnowledgeRaceAnswerPayload
import com.app.messej.data.model.socket.PodiumAssemblyStopSpeakingPayload
import com.app.messej.data.model.socket.PodiumChallengePenaltyKickResultPayload
import com.app.messej.data.model.socket.PodiumChallengePenaltyReadyPayload
import com.app.messej.data.model.socket.PodiumChallengePenaltyTargetPayload
import com.app.messej.data.model.socket.PodiumCommentDeletePayload
import com.app.messej.data.model.socket.PodiumLiveChat
import com.app.messej.data.model.socket.PodiumMaidanLikePayload
import com.app.messej.data.model.socket.SentGiftPayload
import com.app.messej.data.model.socket.YallaCompetitorRequestPayload
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.repository.worker.PodiumLiveWorker
import com.app.messej.data.socket.repository.PodiumChallengeEventRepository
import com.app.messej.data.socket.repository.PodiumEventRepository
import com.app.messej.data.utils.APIUtil.canShowAPIErrorMessage
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumKnowledgeRaceChallengePresenter
import com.app.messej.ui.home.publictab.podiums.challenges.boxChallenge.BoxChallengeBoardModel
import com.app.messej.ui.home.publictab.podiums.challenges.confour.ConnectFourBoard
import com.app.messej.ui.home.publictab.podiums.challenges.confour.ConnectFourBoard.Companion.printableString
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel
import com.app.messej.ui.legal.report.ReportUtils.canBan
import com.app.messej.ui.legal.report.ReportUtils.canReport
import com.app.messej.ui.utils.AsyncExtensions.collectAsync
import com.app.messej.ui.utils.CountryListUtil
import com.app.messej.ui.utils.LocaleUtil
import com.github.f4b6a3.uuid.UuidCreator
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.Duration
import java.time.LocalDateTime
import java.time.ZonedDateTime
import kotlin.math.max
import kotlin.math.roundToInt

@OptIn(FlowPreview::class)
class PodiumLiveViewModel(application: Application) : AndroidViewModel(application) {

    private val podiumRepository = PodiumRepository(application)
    private val podiumEventRepo = PodiumEventRepository
    private val challengeEventRepo = PodiumChallengeEventRepository
    private val profileRepo = ProfileRepository(application)
    private val accountRepo = AccountRepository(application)
    private val dataStore = FlashatDatastore()

    private val _accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val user: CurrentUser get() = accountRepo.user

    private fun Int?.notSelf() = this != user.id
    private fun Int?.isSelf() = this == user.id

    val isVisitorUser: Boolean
        get() = user.citizenship.isVisitor

    private fun AbstractUser?.isSelf() = this?.id == user.id
    private fun AbstractUser?.notSelf() = this?.id != user.id

     val skipNextTime = MutableLiveData(false)

    private val _podiumId = MutableLiveData<String?>(null)
    val podiumId: LiveData<String?> = _podiumId

    private var _countryList: Map<String, Int>? = null

    private fun initCountryList() {
        viewModelScope.launch(Dispatchers.IO) {
            _countryList = CountryListUtil.getCustomCountryMap()
        }
    }

    private val userCountryCode = accountRepo.getAccountDetailsFlow().map {
        Log.w("PLVM", "userCountryCode: $it")
        if (it?.showCountryFlag == true) it.countryCodeIso else null
    }.stateIn(viewModelScope, SharingStarted.Eagerly, null)

    @DrawableRes
    fun getCountryFlag(code: String?): Int? {
//        val random = Random()
//        val cl = _countryList?: return null
//        return cl.entries.elementAt(random.nextInt(cl.size)).value
        code ?: return null
        return _countryList?.get(code)
    }

    var searchWaitingListKeyword = MutableLiveData("")
    private val searchWaitingListTerm = MutableLiveData("")

    val cameraTimeExpireSoon =LiveEvent<Duration>()
    val cameraTimeExpired =LiveEvent<Boolean>()

    private val _podiumHideLiveUsers = MutableLiveData<Boolean>(false)
    val podiumHideLiveUsers: LiveData<Boolean> = _podiumHideLiveUsers

    init {
        ********************************()

        collectAsync(NetworkConnectivityObserver(application).observe()) {
            iAmOnline = it == ConnectivityObserver.ConnectionStatus.AVAILABLE
        }
        collectAsync(PodiumEventService.joinErrorFlow) {
            Toast.makeText(application, it.error.message, Toast.LENGTH_LONG).show()
            checkIfStillLive()
        }
        collectAsync(podiumEventRepo.liveChatFlow.filter { _podiumId.value == it.podiumId }) {
            _liveChat.addAndPost(it)
        }

        collectAsync(podiumEventRepo.newLikeFLow.filter { _podiumId.value == it.podiumId }.debounce(100)) {
            _podium.value?.let { pod ->
                _podium.postValue(
                    pod.copy(
                        likes = pod.likes + 1,
                        totalLikes = pod.totalLikes + 1,
                        totalCoins = pod.totalCoins?.plus(1) ?: pod.totalCoins
                    )
                )
            }
            if (it.userId.notSelf()) {
                onLikedByParticipant.postValue(true)
            }
        }
        collectAsync(podiumEventRepo.podiumGiftFlow) {
            it ?: return@collectAsync
            if (!it.isInvolved && (it.giftType == GiftType.VIP || it.giftType == GiftType.PERSONAL||it.giftType == GiftType.BANK)) {
                onPodiumGiftSent.postValue(it)
            }
        }
        collectAsync(podiumEventRepo.waitListFlow.filter { _podiumId.value == it.first }) {
            Log.w("SOCKETs", "waitListFlow: ${it.first} | ${it.second}")
            _waiters.addAndPost(it.second)
            if (iAmElevated.value == true) {
                onNewSpeakRequest.postValue(it.second.id)
            }
        }

        collectAsync(podiumEventRepo.exitWaitListFlow.filter { _podiumId.value == it.first }) {
            _waiters.removeAndPost(it.second)
        }

        collectAsync(podiumEventRepo.speakerFlow.filter { _podiumId.value == it.first }) {
            Log.w("SOCKETs", "speakerFlow: ${it.first} | ${it.second}")
            Firebase.crashlytics.log(Gson().toJson(it.second))
            _speakers.addAndPostSpeaker(it.second)
            _waiters.removeAndPost(it.second.id)
        }

        collectAsync(podiumEventRepo.editedPodiumDetails.filter { it.id == _podiumId.value }) { updatedPod ->
            _podium.value?.let { pod ->
               _podium.postValue(
                    pod.copy(
                        requiredUserRating = updatedPod.requiredUserRating,
                        requiredRatingToComment = updatedPod.requiredRatingToComment,
                        requiredRatingToSpeak = updatedPod.requiredRatingToSpeak,
                        joiningFee = updatedPod.joiningFee,
                        audienceFee = updatedPod.audienceFee,
                        stageFee = updatedPod.stageFee
                    )
                )
                Log.d("PLVM", "Edited Podium details -> $pod")
            }
        }

        var autoCloseJob: Job? = null

        collectAsync(podiumEventRepo.syncFlow.filter { _podiumId.value == it.podiumId }) { it ->
            if(it.speakingTimeRemaining != 0L && hasPausedAssemblySpeaker.value == true) {
                onAssemblyPausedSpeaking.postValue(it.speakingTimeRemaining)
            }
            val filteredAdminList = it.adminList.filter { it != podium.value?.managerId }
            _adminIdList = filteredAdminList.toMutableList()
            _userPauseGiftList = it.giftsPausedParticipants.orEmpty()

            Log.w("PSP", "syncFlow")
            if (podiumKind.value!=PodiumKind.ALONE) {
                _podiumAboutToClose.postValue(it.aboutToClose)
                _podium.value?.let { pod ->
                    if (it.aboutToClose && pod.isManagerCompat && autoCloseJob?.isActive!=true) {
                        autoCloseJob = viewModelScope.launch {
                            it.willAutoCloseIn?.let { ac ->
                                delay(ac.toMillis())
                                close()
                            }
                        }
                    } else {
                        autoCloseJob?.cancel()
                        autoCloseJob = null
                    }
                }
            } else {
                autoCloseJob?.cancel()
            }
            _podium.value?.let { pod ->
                _podium.postValue(
                    pod.copy(
                        likes = it.likes,
                        totalCoins = it.totalCoins,
                        liveUsers = it.liveUsers,
                        totalUsers = it.totalUsers,
                        giftCount = it.giftCount,

                        talkTimeDuration = it.talkTimeDuration,
                        talkTimeStart = it.talkTimeStart,

                        competitorUserId = if(pod.kind==PodiumKind.MAIDAN) it.speakerList.firstOrNull { !it.isManager }?.id else null,
                        competitorMuted = it.competitorMuted,
                        maidanStatus = it.maidanStatus,
                        podiumGiftPaused = it.podiumGiftPaused,
                        allowYallaGuys = it.allowYallaGuys!=false,
                        yallaChallengesCount = it.yallaChallengesCount
                    )
                )
            }
            Firebase.crashlytics.log("Sync Event")
            Firebase.crashlytics.log(Gson().toJson(it.speakerList))
            _speakers.mapAndPost(it.speakerList.map { ps ->
                if (ps.id.isSelf() && !isAssemblyPodium && !isTheaterPodium) {
                    // will not set muted if local value exists
                    ps.muted = meAsSpeaker.value?.muted ?: ps.muted
                }
                if (isAdmin(ps.id)) {
                    ps.role = Podium.PodiumUserRole.ADMIN
                }
                ps
            }.toMutableList())
            speakerInvites = it.speakerInvites.orEmpty()
            Log.d("PLVM-IS", "syncflow: invites | $speakerInvites")
            _podiumHideLiveUsers.postValue(it.liveUserHide)
        }

        collectAsync(podiumEventRepo.exitSpeakerFlow.filter { _podiumId.value == it.podiumId }) {
            _speakers.removeAndPostSpeaker(it.userId)

            if (it.userId.isSelf() && iAmSpeaker.value == true && it.endBy!=null && it.endBy.notSelf()) {
                val endByManager = isManager(it.endBy)
                onIGotRemovedFromSpeaker.postValue(endByManager)
            }
        }

        collectAsync(podiumEventRepo.mainScreenFLow.filter { _podiumId.value == it.podiumId }) {
            val speakers = _speakers.value.orEmpty()
            speakers.forEach { sp ->
                if(it.userId == sp.speaker.id) {
                    sp.speaker.shownOnMainScreen = true
                } else {
                    sp.speaker.shownOnMainScreen = false
                }
            }
        }

        collectAsync(podiumEventRepo.leaveFlow.filter { _podiumId.value == it.first && it.second.notSelf() }) {
            _waiters.removeAndPost(it.second)
            _speakers.removeAndPostSpeaker(it.second)
        }

        collectAsync(podiumEventRepo.freezeUserFlow.filter { it.podiumId == _podiumId.value}) {
            if(it.userId.isSelf()) {
                var frozenByManager: Boolean? = null
                it.frozenBy?.let { frozenBy ->
                    frozenByManager = isManager(frozenBy)
                }
                isChatFrozen.postValue(Pair(it.frozen, frozenByManager))
            } else {
                _liveChat.value?.forEach { chat ->
                    if (chat.userId == it.userId) {
                        chat.chatFrozenForUser = it.frozen == true
                    }
                }
            }
        }

        collectAsync(podiumEventRepo.chatEnabledFlow.filter { it.podiumId == _podiumId.value }) {
            val pod = _podium.value?: return@collectAsync
            val newPod = pod.copy(
                chatDisabled = !it.enabled,
                chatDisabledBy = when (it.role) {
                    Podium.PodiumUserRole.MANAGER -> pod.managerId
                    Podium.PodiumUserRole.ADMIN -> _adminIdList.getOrElse(0) { 0 }
                    else -> 0
                }
            )
            _podium.postValue(newPod)
            onChatDisabled.postValue(Pair(!it.enabled, it.role))
            if(iAmManager.value==true) return@collectAsync
            if(it.enabled==false) {
            _liveChat.postValue(mutableListOf())
            }
        }
        collectAsync(podiumEventRepo.likeDisabledFlow.filter { it.podiumId == _podiumId.value }) {
            val newPod = _podium.value?.copy(
                likesDisabled = it.disabled
            )
            _podium.postValue(newPod)
            onLikesDisabled.postValue(Pair(it.disabled, it.role))
        }

        collectAsync(podiumEventRepo.micEnabledFlow.filter { it.podiumId == _podiumId.value }) {
            Log.i("testing value", it.disabled.toString())
            val newPod = _podium.value?.copy(
                requestToSpeakDisabled = it.disabled
            )
            _podium.postValue(newPod)
            onMicEnabled.postValue(Pair(it.disabled, it.role))
        }

        collectAsync(podiumEventRepo.muteUserFlow.filter { it.podiumId == _podiumId.value }) { pl ->
            Log.w("PLVM", "muteUserFlow: $pl")
            var mutedByManager: Boolean? = null
            pl.mutedBy?.let { muteBy ->
                mutedByManager = isManager(muteBy)
            }
            _speakers.value?.find { it.speaker.id == pl.userId }?.apply {
//                        Log.w("PLVM", "muteUserFlow: setting ${pl.userId} muted: ${pl.muted}, currentlySpeaking: ${!pl.muted && currentlySpeaking}", )

                muted = pl.muted
                currentlySpeaking = !pl.muted && currentlySpeaking
            }
            if (pl.userId.isSelf()) {
                agoraSession?.muteAudio(pl.muted)
                onAdminMuted.postValue(Pair(pl.muted, mutedByManager))
            }
        }

        collectAsync(podiumEventRepo.podiumActiveFlow.filter { it == _podiumId.value }) {
            _podiumAboutToClose.postValue(false)
        }

        viewModelScope.launch(Dispatchers.Default) {
            try {
                val maxTTL = 4
                AgoraEngineService.volumeInfoFlow.map { list ->
                    return@map list.map {
                        it.copy(
                            userId = if (it.userId == 0) user.id else it.userId
                        )
                    }
                }.collect { pl ->
                    withContext(Dispatchers.Main) {
                        val csu = currentlySpeakingUsers.map {
                            it.ttl--
                            it
                        }.filter { it.ttl > 0 }.toMutableList()
                        pl.forEach { plu ->
                            csu.find { it.userId == plu.userId }?.also {
                                it.ttl = maxTTL
                            } ?: run {
                                csu.add(VolumeInfoWithTTL(plu.userId, plu.volume, maxTTL))
                            }
                        }
//                    Log.w("PLVM", "currentlySpeakingUsers: $csu", )
                        currentlySpeakingUsers = csu
                        _speakers.value?.map { ps ->
//                        Log.w("PLVM", "volumeInfoFlow: setting currentlySpeaking: !${ps.muted} && ${csu.find { it.userId == ps.speaker.id }}!= null | ${!ps.muted && csu.find { it.userId == ps.speaker.id } != null}", )
                            ps.currentlySpeaking = !ps.muted && csu.find { it.userId == ps.speaker.id } != null
                            ps
                        }
                    }
                }
            } finally {
            }
        }

        collectAsync(podiumEventRepo.closeFlow.filter { it.first == _podiumId.value }) {
            (it.second?:PodiumClosedBy.SYSTEM).let { reason ->
                kickFromPodium(PodiumKickReason.PodiumClosed(reason))
            }
        }

        collectAsync(podiumEventRepo.blockedFlow.filter { it.podiumId == _podiumId.value }) { pl ->
            if (pl.userId.isSelf()) {
                podiumEventRepo.leavePodium(pl.podiumId, user.id)
                kickFromPodium(PodiumKickReason.PodiumBlocked)
            }
            else {
                _liveChat.value?.let { lc->
                    _liveChat.postValue(lc.filter { it.userId != pl.userId }.toMutableList())
                }
            }
        }

        collectAsync(podiumEventRepo.appointAdminFLow.filter { it.podiumId == _podiumId.value }) { pl ->
            if (pl.userId.isSelf()) {
                _podium.value?.let { pod ->
                    _podium.postValue(
                        pod.copy(
                            invitedToBeAdmin = true
                        )
                    )
                }
            }
            _invitedAdminIdList.add(pl.userId)
        }

        collectAsync(podiumEventRepo.newAdminFLow.filter { it.podiumId == _podiumId.value }) { pl ->
            _podium.value?.let {
                //Add id to admin list
                _adminIdList.add(pl.userId)
                //remove id from invited admin list
                _invitedAdminIdList.removeIf { it == pl.userId }
            }
        }

        collectAsync(podiumEventRepo.cancelAdminRequestFLow.filter { it.podiumId == _podiumId.value }) { pl ->
            if (pl.userId.isSelf()) {
                _podium.value?.let { pod ->
                    _podium.postValue(
                        pod.copy(
                            invitedToBeAdmin = false
                        )
                    )
                }
            }
            _invitedAdminIdList.removeIf { it == pl.userId }
        }

        collectAsync(podiumEventRepo.dismissAdminFLow.filter { it.podiumId == _podiumId.value }) { pl ->
            if (pl.userId.isSelf()) {
                _podium.value?.let { pod ->
                    _podium.postValue(
                        pod.copy(
                            role = if (pod.isInvited) Podium.PodiumUserRole.INVITEE else Podium.PodiumUserRole.AUDIENCE
                        )
                    )
                }
            }
            _adminIdList.removeIf { it == pl.userId }
        }
        collectAsync(challengeEventRepo.challengeUpdateFlow) {
            Log.d("PCP", "challengeUpdateFlow: $it")
            postChallenge(it)
        }
        collectAsync(challengeEventRepo.challengeCloseFlow.filter { it.challengeId == activeChallenge.value?.challengeId }) {
            postChallenge(null)
        }

        collectAsync(challengeEventRepo.challengeFacilitatorRequestFlow.filter { it.podiumId == _podiumId.value }) { pl ->
            Log.d(
                "FAlert",
                "init: isExpired : ${pl.facilitator?.isExpired} , timeRemaining : ${pl.facilitator?.timeRemaining}, parsedTime : ${pl.facilitator?.parsedRequestedTime}, timeFromBE : ${pl.facilitator?.timeRequestSent}"
            )
            if (pl.facilitator!=null && pl.facilitator.id.isSelf() && !pl.facilitator.isExpired && pl.facilitator.requestAccepted!=true) {
                onChallengeFacilitatorRequest.postValue(Pair(pl.challengeType,pl.facilitator.timeRemaining))
            }
        }

        collectAsync(challengeEventRepo.challengeContributorRequestFlow.filter { it.podiumId == _podiumId.value || it.parentPodiumId == _podium.value?.parentId }) { pl ->
            val contributor = pl.contributors.find { it.id.isSelf() }
            //check if I am one of the contributors, and I am not the facilitator (as facilitator will have already accepted)
            if (contributor != null && contributor.timeResponded == null && contributor.id != pl.facilitator?.id) {

                onChallengeContributorRequest.postValue(Pair(pl, contributor))
            }
            // if I am the facilitator, start a timer to detect the timeout
            if (pl.facilitator?.id.isSelf() && pl.contributorType == ChallengeContributionType.SPEAKERS) { //case to show the contributor fee bottomSheet for facilitator
                if (pl.contributorRequestedTimeRemaining.seconds > 0) {
                    viewModelScope.launch {
                        delay(pl.contributorRequestedTimeRemaining.toMillis())
                        onContributorRequestToSpeakersTimedOut.postValue(true)
                    }
                }
            }
            // if I am the facilitator, start a timer to detect the timeout
            else if (pl.facilitator?.id.isSelf() && pl.contributorType == ChallengeContributionType.CONTRIBUTOR) {
                if (pl.contributorRequestedTimeRemaining.seconds > 0) {
                    viewModelScope.launch {
                        delay(pl.contributorRequestedTimeRemaining.toMillis().plus(2000)) //Added an extra 2 second delay for the time out
                        checkAndShowContributorTimedOut()
                    }
                }
            }
        }

        collectAsync(challengeEventRepo.challengeSetupEventFlow.filter { it.challengeId == activeChallenge.value?.challengeId }) { pl ->
            onChallengeSetupEvent.postValue(pl)
        }

        collectAsync(challengeEventRepo.challengeScoreUpdateFlow.filter { it.podiumId == _podiumId.value }) { pl ->
            onChallengeScoreUpdate.postValue(pl)
        }
        collectAsync(challengeEventRepo.challengeSyncFlow.filter { it.challengeId == activeChallenge.value?.challengeId }) { pl ->
            val challenge = _activeChallenge.value?: return@collectAsync
            if(pl.isOutdated(challenge)) {
                Log.e("PLVM","Outdated Sync Event: \n Current: \n $challenge \n Received: \n $pl")
                return@collectAsync
            }
            _activeChallenge.postValue(
                challenge.copy(
                    _status = pl.status,
                    endTimeUTC = pl.endTimeUTC?:challenge.parsedEndTime?.toEpochSecond(),
                    topSupporters = pl.topSupporters,
                    _participantScores = pl.participantScores,
                    conFourData = pl.connectFourData,
                    penaltyData = pl.penaltyData,
                    boxData = pl.boxData
                )
            )
            Log.d("PCPC4", "challengeSyncFlow: ${pl.participantScores} | $ \n${pl.connectFourData?.board?.printableString()}")
        }

        collectAsync(podiumEventRepo.speakerInviteFlow.filter { it.podiumId == _podiumId.value && it.inviteeId.isSelf()}) { pl ->
            onSpeakerInvite.postValue(Podium.SpeakerInvite(
                invitedBy = pl.invitedBy,
                inviteeId = pl.inviteeId,
                timeInvited = pl.invitedTime,
                accepted = false,
                joinType = pl.joinType,
                invitedForFree = pl.invitedForFree,
                inviteFee = pl.inviteFee
            ))
        }

        collectAsync(podiumEventRepo.speakerInviteResponseFlow.filter { it.podiumId == _podiumId.value }) { pl ->
            if (pl.invitedBy.isSelf()) {
                onSpeakerInviteResponse.postValue(Pair(pl.action, pl.inviteeName))
            }
        }

        collectAsync(challengeEventRepo.challengeSpeakerInviteFlow.filter { it.podiumId == _podiumId.value }) { pl ->
            if (pl.inviteeId.isSelf()) {
                onChallengeSpeakerInvite.postValue(Pair(pl.challengeType, pl.conFourParticipantRequestedTimeRemaining))
            }

            // To update invited time in challenge object after invitee is invited
            val challenge = activeChallenge.value?: return@collectAsync
            _activeChallenge.postValue(
                _activeChallenge.value?.copy(
                    invitedParticipantsTime = pl.invitedParticipantsTime,
                    screen = PodiumChallenge.ChallengeScreen.INVITE_PARTICIPANTS
                )
            )
        }

        collectAsync(challengeEventRepo.challengeSpeakerInviteResponseFlow.filter { it.podiumId == _podiumId.value }) { pl ->
            _activeChallenge.value?.let { ch ->
                ch.waitingParticipants?.find { it.id == pl.inviteeId }?.requestAccepted = pl.action == AcceptDecline.ACCEPT
                postChallenge(ch)
                if (pl.invitedBy.isSelf()) {
                    onChallengeSpeakerInviteResponse.postValue(Pair(pl.action, pl.inviteeName))
                    if (activeChallenge.value?.challengeType in listOf(ChallengeType.CONFOUR, ChallengeType.PENALTY, ChallengeType.BOXES, ChallengeType.KNOWLEDGE) && pl.participantsCount >= 2) {
                        onMinimumParticipantsAccepted.postValue(true)
                    }
                }
            }
        }

        collectAsync(challengeEventRepo.conFourTokenDroppedFlow.filter { it.challengeId == activeChallenge.value?.challengeId }) { pl ->
            _activeChallenge.value?.let {
                it.conFourData = pl.confourData
            }
            onConnectFourChallengeCoinDropped.postValue(pl)
        }

        collectAsync(challengeEventRepo.boxLineDrawnFlow.filter { it.challengeId == activeChallenge.value?.challengeId }) { pl ->
            _activeChallenge.value?.let {
                it.boxData = pl.boxData
            }
            onBoxChallengeLineDrawn.postValue(pl)
        }

        collectAsync(challengeEventRepo.knowledgeRaceUpdateFlow.filter { it.challengeId == activeChallenge.value?.challengeId }) { pl ->
            _activeChallenge.value?.let {
                _activeChallenge.postValue(pl.updateIn(it))
            }
            pl.eliminations?.let { el ->
                onKnowledgeRaceElimination.postValue(el)
            }
        }

        collectAsync(challengeEventRepo.challengeGameOverFlow.filter { it.challengeId == activeChallenge.value?.challengeId }) { pl ->
            Log.w("PLVM_GO","challengeGameOverFlow: $pl")
            _activeChallenge.value?.let {
                val new = it.copy(
                    _status = PodiumChallenge.ChallengeStatus.GAME_OVER,
                    _participantScores = pl.participantScores
                )
                if(pl.reason== ChallengeGameOverPayload.GameOverReason.PARTICIPANT_EXITED_CHALLENGE) {
                    onChallengeParticipantExit.postValue(pl.userId)
                }
                postChallenge(new)
                onChallengeGameOver.postValue(pl.participantScores.orEmpty())
            }
        }

        collectAsync(podiumEventRepo.startSpeakingFlow.filter { it.podiumId == _podiumId.value }) { pl ->
            _speakers.value?.map {
                if(it.speaker.id == pl.userId) {
                    if(it.speaker.originalStatus != null) {
                        //paused case
                        it.speaker.speakingStatus = it.speaker.originalStatus
                        it.assemblySpeakingStatus = it.speaker.originalStatus
                        if(it.speaker.originalStatus == AssemblySpeakingStatus.ACTIVE && it.speaker.id.isSelf()) {
                            agoraSession?.muteAudio(false)
                        }
                    } else {
                        //normal start case
                        it.speaker.speakingStatus = AssemblySpeakingStatus.WAITING
                        it.assemblySpeakingStatus = AssemblySpeakingStatus.WAITING
                    }
                    it.speaker.speakingStartTime = pl.speakingStartTime
                } else if(it.assemblySpeakingStatus == AssemblySpeakingStatus.ACTIVE || it.assemblySpeakingStatus == AssemblySpeakingStatus.WAITING) {
                    it.assemblySpeakingStatus = AssemblySpeakingStatus.INACTIVE
                }
            }
            if (pl.userId.notSelf()) {
                agoraSession?.muteAudio(true)
            } else {
                myTurnStarted.postValue(true)
            }
            resetCurrentActiveSpeaker()
            _speakers.postValue(_speakers.value)
            onManagerSkipEnabled.postValue(false)
        }

        collectAsync(podiumEventRepo.extendSpeakingTimeFlow.filter { it.podiumId == _podiumId.value }) { pl ->
            _speakers.value?.find { it.speaker.id == pl.userId }?.apply {
                speaker.speakingStatus = pl.speakingStatus
                speaker.speakingTimeLimit = pl.speakingTimeLimit
                resetCurrentActiveSpeaker()
            }
            if(pl.userId.isSelf()) {
                onAssemblySpeakingTimeExtended.postValue(true)
            }
            _speakers.postValue(_speakers.value)
        }

        collectAsync(podiumEventRepo.pauseSpeakingFlow.filter { it.podiumId == _podiumId.value }) { pl ->
            _speakers.value?.map {
                if(it.speaker.id == pl.userId ) {
                    it.speaker.speakingStatus = AssemblySpeakingStatus.PAUSED
                    it.assemblySpeakingStatus = AssemblySpeakingStatus.PAUSED
                }
                onAssemblyPausedSpeaking.postValue(pl.speakingTimeRemaining)
                if(pl.userId.isSelf()) {
                    iAmPaused.postValue(true)
                    agoraSession?.muteAudio(true)
                }
            }
            _speakers.postValue(_speakers.value)
        }
        collectAsync(challengeEventRepo.userJoinedFlow.filter { it.podiumId == _podiumId.value }.debounce(1500L)) {
//            if (activeChallenge.value?.challengeType == ChallengeType.CONFOUR) {
                try {
                    it.userDetails.countryCode?.let { cc ->
                        it.userDetails.countryFlag = _countryList?.get(cc)
                    }
                    onUserJoined.postValue(it.userDetails)
                } catch (e: Exception) {
                    Firebase.crashlytics.log(it.toString())
                    Firebase.crashlytics.recordException(e)
                }
//            }
        }

        collectAsync(challengeEventRepo.playerReadyFlow.filter { it.challengeId == activeChallenge.value?.challengeId }) {
            onPenaltyPlayerReady.postValue(it.userId)
        }

        collectAsync(challengeEventRepo.penaltyStartTurnFlow.filter { it.challengeId == activeChallenge.value?.challengeId }) { pl ->
            _activeChallenge.value?.let {
                it.penaltyData = pl.penaltyData
            }
            onPenaltyStartTurn.postValue(pl.penaltyData)
        }

        collectAsync(challengeEventRepo.penaltySelectTargetFlow.filter { it.challengeId == activeChallenge.value?.challengeId }) { pl ->
            onPenaltySelectTarget.postValue(pl)
        }

        collectAsync(challengeEventRepo.penaltyKickResultFlow.filter { it.challengeId == activeChallenge.value?.challengeId }) { pl ->
            _activeChallenge.value?.let {
                it.penaltyData = pl.penaltyData
            }
            onPenaltyKickResult.postValue(pl)
        }

        collectAsync(podiumEventRepo.cameraTimeExpiredSoon.filter { it.podiumId == _podiumId.value }) {
            Log.i("testing value", it.cameraExpiry)
            allSpeakers.value?.find { sp ->
                sp.speaker.id == it.userId
            }?.let { sp->
               sp.speaker.cameraExpiry = it.cameraExpiry
            }
            if(it.userId.isSelf()) {
                val minutesUntilExpiry = Duration.between(LocalDateTime.now(), it.parsedExpiryTime)    /**Balance minutes left*/
                cameraTimeExpireSoon.postValue(minutesUntilExpiry)
            }
        }
        collectAsync(podiumEventRepo.cameraTimeExpired.filter { it.podiumId == _podiumId.value }) {
            Log.i("testing value", it.cameraExpiry)
            allSpeakers.value?.find { sp ->
                sp.speaker.id == it.userId
            }?.let { sp->
                sp.speaker.cameraExpiry = it.cameraExpiry
            }
            cameraTimeExpired.postValue(true)
        }
        collectAsync(podiumEventRepo.podiumChatDelete.filter { it.podiumId == _podiumId.value  }){
            Log.i("testing value", it.chatId)
                deleteChatById(it.chatId)
        }
        collectAsync(podiumEventRepo.podiumPauseGift.filter { it.podiumId == _podiumId.value  }){
            Log.i("testing value", it.categoryType)
            val newPod = _podium.value?.copy(
                podiumGiftPaused = it.giftPaused
            )
            _podium.postValue(newPod)
            onGiftPaused.postValue(it.giftPaused)
        }
        collectAsync(challengeEventRepo.maidanLikeFLow.filter { _podiumId.value == it.podiumId || _podium.value?.competitorPodiumId == it.podiumId }) { like ->
            if(like.podiumId==_podiumId.value) {
                _maidanLiveLikes.addAndPost(like)
                _activeChallenge.value?.let { acs ->
                    acs.participantScores.find { it.userId == mainScreenSpeakerId.value }?.let {
                        it.score += (like.maidanLike?.count ?: 0)
                    }
                    _activeChallenge.postValue(acs)
                }
            } else if(like.podiumId == _podium.value?.competitorPodiumId) {
                _activeChallenge.value?.let { acs ->
                    acs.participantScores.find { it.userId == secondMainScreenSpeakerId.value }?.let {
                        it.score += (like.maidanLike?.count ?: 0)
                    }
                    _activeChallenge.postValue(acs)
                }
            }
        }

        collectAsync(challengeEventRepo.yallaCompetitorRequestFlow.filter { _podiumId.value == it.podiumId }){ pl ->
            if (pl.player.userId.isSelf()) return@collectAsync
            onYallaCompetitorRequest.postValue(pl)
        }

        collectAsync(challengeEventRepo.yallaNotificationFlow.filter { _podiumId.value == it }){ id ->
            onYallaNotification.postValue(id)
        }

        initCountryList()
        viewModelScope.launch {
            profileRepo.refreshAccountDetails()
        }

//        viewModelScope.launch {
//            searchContributorKeyword.asFlow().debounce(500L).collect {
//                searchTerm.postValue(it.orEmpty())
//            }
//        }

        viewModelScope.launch {
            searchWaitingListKeyword.asFlow().debounce(500L).collect {
                searchWaitingListTerm.postValue(it.orEmpty())
            }
        }
    }

    val podiumDetailsLoading = MutableLiveData(false)
    val onPodiumLoadError = LiveEvent<String>()
    val onReadyToJoin = LiveEvent<Podium>()

    val onSpeakerInvite = LiveEvent<Podium.SpeakerInvite>()
    val onSpeakerInviteResponse = LiveEvent<Pair<AcceptDecline, String>>()
    val onChallengeSpeakerInvite = LiveEvent<Pair<ChallengeType, Long>>()
    val onChallengeSpeakerInviteResponse = LiveEvent<Pair<AcceptDecline, String>>()
    val onMinimumParticipantsAccepted = LiveEvent<Boolean>()

    var onUserJoined = LiveEvent<PodiumParticipant>()

    fun setPodiumId(id: String) {
        if (_podiumId.value == id) return
        _podiumId.value = id
        loadPodiumDetails(id)
    }

    val podiumUserRatingError = LiveEvent<PodiumWhoCanJoin?>()

    fun softLoadPodiumDetails(id: String) {
        Log.d("PLF", "softLoadPodiumDetails: $id")
        viewModelScope.launch(Dispatchers.IO) {
            podiumRepository.getPodiumById(id)?.let {
                _podium.postValue(if (!it.isLive) it.cleanedCopy() else it)

                val userRating = user.userRatingPercent.toInt()
                val podiumRequiredRating = it.requiredUserRating ?: 0

                if (!it.isAdmin && !it.isManager) {
                    when {
                        podiumRequiredRating == 100 && userRating != 100 -> {
                            podiumUserRatingError.postValue(PodiumWhoCanJoin.RATING_ONLY_HUNDRED)
                            return@launch
                        }
                        podiumRequiredRating > userRating -> {
                            podiumUserRatingError.postValue(PodiumWhoCanJoin.RATING_ABOVE_NINETY)
                            return@launch
                        }
                    }
                }

                it.kind?.let { onPodiumKindFound.postValue(it) }
                return@launch
            }
            getPodiumDetails(id, true)?.let {
                val userRating = user.userRatingPercent.toInt()
                val podiumRequiredRating = it.requiredUserRating ?: 0

                if (!it.isAdmin && !it.isManager) {
                    when {
                        podiumRequiredRating == 100 && userRating != 100 -> {
                            podiumUserRatingError.postValue(PodiumWhoCanJoin.RATING_ONLY_HUNDRED)
                            return@launch
                        }
                        podiumRequiredRating > userRating -> {
                            podiumUserRatingError.postValue(PodiumWhoCanJoin.RATING_ABOVE_NINETY)
                            return@launch
                        }
                    }
                }
                it.kind?.let { onPodiumKindFound.postValue(it) }
            }
        }
    }

    private val _podium = MutableLiveData<Podium?>(null)
    val podium: LiveData<Podium?> = _podium

    val onPodiumKindFound = LiveEvent<PodiumKind>()

    val podiumKind = podium.map {
        it ?: return@map null
        return@map it.kind
    }.distinctUntilChanged()

    private val isLecturePodium: Boolean
        get() = podiumKind.value == PodiumKind.LECTURE

    private val isAssemblyPodium: Boolean
        get() = podiumKind.value == PodiumKind.ASSEMBLY

    private val isTheaterPodium: Boolean
        get() = podiumKind.value == PodiumKind.THEATER

    private var agoraToken: String? = null

    fun refreshPodiumDetails() {
        val podiumId = _podiumId.value?: return
        viewModelScope.launch(Dispatchers.IO) {
            getPodiumDetails(podiumId)
        }
    }

    var skipPodiumSwipeAlertBox : Boolean = false
    fun ********************************() {
        viewModelScope.launch {
            dataStore.getPodiumSwipeCheckBoxAction().collect {
                skipPodiumSwipeAlertBox = it
            }
        }
    }

    fun setPodiumSwipeAlertBoxVisibility(isHidden: Boolean) {
        viewModelScope.launch {
            dataStore.savePodiumSwipeCheckBoxAction(isHidden = isHidden)
        }
    }

    private fun loadPodiumDetails(id: String) {
        podiumDetailsLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            podiumRepository.getPodiumById(id)?.let {
                _podium.postValue(if (!it.isLive) it.cleanedCopy() else it)
            }

            fun handleAPIResult(result: PodiumRepository.JoinResultOf<PodiumJoinResponse>, kickReason:(String?)-> PodiumKickReason): PodiumRepository.JoinResultOf.Success<PodiumJoinResponse>? {
                Log.w("PLVM", "handleAPIResult: API result: $result", )
                val successRes = when(result) {
                    is PodiumRepository.JoinResultOf.Success -> {
                        result
                    }
                    is PodiumRepository.JoinResultOf.APIError -> {
                        if (result.error.result?.reason==PodiumJoinErrorResponse.PodiumJoinErrorReason.LIVE_IN_OTHER_PODIUM) {
                            result.error.result.podiumId?.let { id ->
                                result.error.result.podiumName?.let { name ->
                                    kickFromPodium(PodiumKickReason.LiveInAnotherPodium(result.error.message,id,name,result.error.result.canLeave?:false))
                                    return null
                                }
                            }
                        }
//                        Firebase.crashlytics.recordException(Exception("Failed to Go Live Podium: ${result.error.message}"))
                        kickFromPodium(kickReason.invoke(result.error.message))
                        null
                    }
                    is PodiumRepository.JoinResultOf.Error -> {
//                        Firebase.crashlytics.recordException(Exception("Failed to Go Live Podium: ${result.exception}"))
                        kickFromPodium(kickReason.invoke(null))
                        null
                    }
                }
                return successRes
            }

            getPodiumDetails(id)?.also { pod ->
                if (!pod.isLive) pod.likes = 0
                agoraToken = pod.agoraToken
                if (pod.entry?.canAccept(user.profile.gender)==false) {
                    kickFromPodium(PodiumKickReason.MismatchedGender(pod.entry))
                    return@launch
                }
                if (pod.isManagerCompat || pod.isAdmin) {
                    if (!pod.isLive) {
                        val result = podiumRepository.goLive(id)
                        val successResult = handleAPIResult(result){
                            PodiumKickReason.GoLiveFailed
                        }?: return@launch
                        agoraToken = successResult.value.token
                    } else {
                        val result = podiumRepository.joinPodium(id)
                        val successResult = handleAPIResult(result){
                            PodiumKickReason.JoinFailed(it)
                        }?: return@launch
                        agoraToken = successResult.value.token
                    }
                } else {
                    if (pod.isLive) {
                        val result = podiumRepository.joinPodium(id)
                        val successResult = handleAPIResult(result){
                            PodiumKickReason.JoinFailed(it)
                        } ?: return@launch
                        val rating = accountRepo.getAccountDetailsFlow().firstOrNull()?.flaxRatePercentage

                        if (successResult.value.joinHidden != true) {
                            sendChatMessage(
                                chatType = PodiumLiveChatType.USER_JOIN,
                                userStats = UserStats(rating, successResult.value.generosity, successResult.value.skills)
                            )
                        }
                        if (pod.role == Podium.PodiumUserRole.INVITED) {
                            val newPod = pod.copy(
                                role = Podium.PodiumUserRole.INVITEE
                            )
                            _podium.postValue(newPod)
                            podiumRepository.updatePodium(pod)
                        }
                        agoraToken = successResult.value.token
                    } else {
                        kickFromPodium(PodiumKickReason.NotLive)
                        return@launch
                    }
                }
                withContext(Dispatchers.Main) {
                    Firebase.crashlytics.log("Podium Details")
                    Firebase.crashlytics.log(Gson().toJson(pod.speakers))
                    _speakers.mapAndPost(pod.speakers.toMutableList())
                    speakerInvites = pod.speakerInvites.orEmpty()
                    _userPauseGiftList = pod.giftsPausedParticipants.orEmpty()
                    Log.d("PLVM-IS", "loadPodiumDetails: invites | $speakerInvites")
                }
                isChatFrozen.postValue(Pair(pod.chatFrozen, null))
                onReadyToJoin.postValue(pod)
                if (pod.isLive) {
                    podiumDetailsLoading.postValue(false)
                }
                withContext(Dispatchers.Main) {
                    getPodiumDetails(id)?.let {
                        _speakers.mapAndPost(it.speakers.toMutableList())
                        speakerInvites = pod.speakerInvites.orEmpty()
                        Log.d("PLVM-IS", "loadPodiumDetails: invites | $speakerInvites")
                    }
                }
                podiumDetailsLoading.postValue(false)
                getPodiumWaitList(id)
                getPodiumAdminsList()
                startLiveTimer()
                checkFollowStatus(pod)
            }
        }
    }

    sealed class PodiumKickReason {
        data object NotLive: PodiumKickReason()
        data class JoinFailed(
            val message: String?
        ): PodiumKickReason()
        data object GoLiveFailed: PodiumKickReason()
        data class LiveInAnotherPodium(
            val message: String,
            val otherPodiumId: String,
            val otherPodiumName: String,
            val canLeave: Boolean
        ): PodiumKickReason()
        data class PodiumClosed(
            val closedBy: PodiumClosedBy
        ): PodiumKickReason()
        data object PodiumClosedByMe: PodiumKickReason()
        data object PodiumBlocked: PodiumKickReason()
        data class MismatchedGender(
            val entry: PodiumEntry
        ): PodiumKickReason()
    }

    private var previousKickReason: PodiumKickReason? = null

    fun resetKicks() {
        previousKickReason = null
    }
    private fun kickFromPodium(reason: PodiumKickReason, force: Boolean = false) {
        Log.e("PLVM", "kickFromPodium: $reason", )
        viewModelScope.launch(Dispatchers.IO) {
            Log.d("PLW", "calling end")
            PodiumLiveWorker.endAll()
        }
        if(reason is PodiumKickReason.PodiumClosed && reason.closedBy == PodiumClosedBy.MANAGER) {
            if (_closingPodium.value==true) return
        }
        if (previousKickReason!=null && !force) return
        agoraSession?.leave()
        previousKickReason = reason
        onKickedFromPodium.postValue(reason)
    }

    val onKickedFromPodium = LiveEvent<PodiumKickReason>()

    private val _podiumAboutToClose = MutableLiveData<Boolean>(false)
    val podiumAboutToClose: LiveData<Boolean> = _podiumAboutToClose.distinctUntilChanged()

    val onIGotRemovedFromSpeaker = LiveEvent<Boolean?>()

    private var iAmOnline = true

    data class VolumeInfoWithTTL(
        val userId: Int,
        val volume: Int,
        var ttl: Int,
    )

    private var currentlySpeakingUsers: MutableList<VolumeInfoWithTTL> = mutableListOf()

    val nickNames = profileRepo.getNickNamesFlow().stateIn(
        scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = listOf()
    )

    private val nickNamesLiveData = nickNames.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    private val _speakers = MutableLiveData<MutableList<ActiveSpeakerUIModel>>(mutableListOf())

    val allSpeakers: LiveData<MutableList<ActiveSpeakerUIModel>> by lazy {
        _speakers.map {
            it.sortSpeakers().toMutableList()
        }
    }

    private fun List<ActiveSpeakerUIModel>.sortSpeakers(): List<ActiveSpeakerUIModel> {
        return if(isAssemblyPodium) this.sortedBy {
            it.speaker.speakingOrder
        }
        else this.sortedBy {
            it.speaker.name
        }.sortedByDescending { sp ->
            if (sp.speaker.shownOnMainScreen) 100
            else when (sp.speaker.role) {
                Podium.PodiumUserRole.MANAGER -> 99
                Podium.PodiumUserRole.ADMIN -> 98
                else -> sp.speaker.citizenship.orDefault().ordinal
            }
        }
    }

    private val showingSpeakerOverlay = MutableLiveData(false)

    fun setShowingSpeakerOverlay(showing: Boolean) {
        showingSpeakerOverlay.postValue(showing)
    }

    val speakers: LiveData<MutableList<ActiveSpeakerUIModel>> by lazy {
        val med = MediatorLiveData<MutableList<ActiveSpeakerUIModel>>()
        fun update() {
            val ps = _speakers.value

            val list = ps.orEmpty().filter {
                Log.d("GOLDEN","Golden check: ${it.speaker.name} is ${it.speaker.citizenship}")
                if (challengeActive.value == true && _activeChallenge.value?.challengeType == ChallengeType.GIFTS && it.speaker.citizenship == UserCitizenship.GOLDEN) {
                    Log.d("GOLDEN","Golden check: false | skipping GOLDEN user in GIFTS")
                    false
                }
                else if (showingSpeakerOverlay.value != true && podiumKind.value?.hasMainScreens != false) {
                    Log.d("GOLDEN","Golden check: ${!it.speaker.shownOnMainScreen && it.speaker.showOnStage != true} | Skip due to being in MainScreen?")
                    !it.speaker.shownOnMainScreen && it.speaker.showOnStage != true
                }
                else {
                    Log.d("GOLDEN","Golden check: true")
                    true
                }
            }.sortSpeakers().toMutableList()
            med.postValue(list)
        }
        med.addSource(_speakers) { update() }
        med.addSource(showingSpeakerOverlay) { update() }
        med.addSource(_activeChallenge) { update() }
        med.distinctUntilChanged()
    }

    val speakersWithPlaceholders: LiveData<MutableList<PodiumSpeakerUIModel>> by lazy {
        val med = MediatorLiveData<MutableList<PodiumSpeakerUIModel>>()
        fun update() {
            val list: MutableList<PodiumSpeakerUIModel> = speakers.value.orEmpty().toMutableList()
            Log.d("PLVM", "speakersWithPlaceholders: $list")
            if (showingSpeakerOverlay.value!=true) {
                podiumKind.value?.let {
                    val padding = it.maxSpeakers -it.mainScreens - list.size
                    list.addAll(Array(padding.coerceAtLeast(0)) {
                        PodiumSpeakerUIModel.EmptyStateSpeakerUIModel
                    })
                }
            }
            med.postValue(list)
        }
        med.addSource(speakers) { update() }
        med.addSource(podiumKind) { update() }
        med
    }

    val mainScreenSpeaker = _speakers.map {
        Log.w("PLVM", "mainScreenSpeaker for: ${podiumKind.value}")
        it.find { sp -> sp.speaker.shownOnMainScreen }
    }

    val mainScreenSpeakerId = mainScreenSpeaker.map {
        it?.speaker?.id
    }.distinctUntilChanged()

    val maidanProfilePic = mainScreenSpeaker.map {
        return@map it?.speaker?.thumbnail
    }

    val isMaidanMainUserPremium = mainScreenSpeaker.map {
        return@map (it?.speaker?.premiumUser ?: false)
    }


    val onNewSpeaker = LiveEvent<Int>()

    private fun List<PodiumSpeaker>.findMainScreenSpeaker(): PodiumSpeaker? {
        if (podiumKind.value==PodiumKind.ASSEMBLY) return null
        return filter { it.shownOnMainScreen }.apply {
            if (this.size>1 && BuildConfig.BUILD_TYPE != Constants.BuildType.RELEASE.value) {
                Firebase.crashlytics.recordException(Exception("More than 1 speaker found to be in Main Screen"))
                Toast.makeText(getApplication(), "!! More than 1 speaker found to be in Main Screen", Toast.LENGTH_SHORT).show()
            }
        }.firstOrNull()
    }

    private fun MutableLiveData<MutableList<ActiveSpeakerUIModel>>.mapAndPost(list: MutableList<PodiumSpeaker>) {
        val csu = currentlySpeakingUsers
        val existingSpeakers = value.orEmpty()
        val mapped = list.distinctBy { it.id }.map { ps ->
            _waiters.removeAndPost(ps.id)
            val uiModel = existingSpeakers.find { it.speaker.id == ps.id } ?: ActiveSpeakerUIModel(
                ps.copy(
                    name = nickNames.nickNameOrName(ps)
                )
            )
            uiModel.apply {
                speaker = ps.copy(
                    name = nickNames.nickNameOrName(ps),
                    role = if(isAdmin(ps)) Podium.PodiumUserRole.ADMIN else ps.role
                )
                muted = ps.muted
                online = if (ps.id.isSelf()) iAmOnline else ps.online != false
                admin = isAdmin(ps)
                isManager = isManager(ps.id)
                coinsToDisplay = ps.coinsReceivedFormatted
                assemblySpeakingStatus = ps.speakingStatus
//                Log.w("PLVM", "mapAndPost: setting currentlySpeaking: ${!ps.muted && csu.find { it.userId == ps.id } != null}", )
                currentlySpeaking = !ps.muted && csu.find { it.userId == ps.id } != null
                ps.countryCode?.let { cc ->
                    countryFlag = _countryList?.get(cc)
                }
            }
        }.toMutableList()
        this.postValue(mapped)
    }

    private fun MutableLiveData<MutableList<ActiveSpeakerUIModel>>.addAndPostSpeaker(item: PodiumSpeaker) {
        if (this.value?.find { it.speaker.id == item.id } != null) return
        this.postValue(this.value?.apply {
            if (find { it.speaker.id == item.id } == null) add(
                ActiveSpeakerUIModel(
                    item.copy(
                        name = nickNames.nickNameOrName(item)
                    )
                )
            )
        })
        onNewSpeaker.postValue(item.id)
    }

    private fun MutableLiveData<MutableList<ActiveSpeakerUIModel>>.removeAndPostSpeaker(id: Int) {
        if (this.value?.find { it.speaker.id == id } == null) return
        this.postValue(this.value?.apply {
            removeIf { it.speaker.id == id }
        })
    }

    fun getSpeaker(id: Int): PodiumSpeaker? {
        return _speakers.value?.find { it.speaker.id == id }?.speaker
    }

    fun isSpeaker(id: Int) = getSpeaker(id)!=null

    fun userHasInvitation(): Boolean {
        return _podium.value?.isInvited == true || _podium.value?.isInvitee == true
    }

    private var speakerInvites: List<Podium.SpeakerInvite> = listOf()

    fun findSpeakerInvite(id: Int): Podium.SpeakerInvite? {
        val invite = speakerInvites.find { it.inviteeId == id }
        Log.d("PLVM-IS", "findSpeakerInvite: $id | $invite | expired: ${invite?.hasExpired}")

        return speakerInvites.find { it.inviteeId == id }
    }

    private val _waiters = MutableLiveData<MutableList<PodiumSpeaker>>(mutableListOf())
    val waiters: LiveData<MutableList<PodiumSpeaker>> = _waiters.map {
        it.map { ps ->
            ps.copy(
                name = nickNames.nickNameOrName(ps)
            )
        }.sortedByDescending { wt ->
            if (wt.id.isSelf()) 2
            else if (wt.premiumUser) 1
            else 0
        }.take(20).toMutableList()
    }


    private val _allWaiters = searchWaitingListTerm.switchMap {
        val podiumId = podiumId.value?: return@switchMap null
        podiumRepository.getPodiumWaitingListPager(podiumId, it).liveData.cachedIn(viewModelScope)
    }

    val allWaiters = _allWaiters.map {
        it.map { ps ->
            ps.copy(
                name = nickNames.nickNameOrName(ps)
            )
        }
    }

    val onNewSpeakRequest = LiveEvent<Int>()

    private fun MutableLiveData<MutableList<PodiumSpeaker>>.addAndPost(item: PodiumSpeaker) {
        this.postValue(this.value?.apply {
            if (find { it.id == item.id } == null) add(item)
        })
    }

    private fun MutableLiveData<MutableList<PodiumSpeaker>>.removeAndPost(id: Int) {
        try {
            if (this.value?.find { it.id == id } == null) return
            this.postValue(this.value?.apply {
                removeAll { it.id == id }
            })
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
        }
    }

    fun getWaiter(id: Int): PodiumSpeaker? {
        return _waiters.value?.find { it.id == id }
    }

    fun findLiveChatUser(id: Int): PodiumLiveChat? {
        return _liveChat.value?.find { it.userId == id }
    }

    val waitersCount: LiveData<String> = _waiters.map {
        return@map "${it.size.coerceAtMost(25)}${if (it.size > 25) "+" else ""}"
    }

    private val _liveChat = MutableLiveData<MutableList<PodiumLiveChat>>(mutableListOf())
    val liveChat: LiveData<MutableList<PodiumLiveChat>> = _liveChat.map {
        it.map { plc ->
            plc.senderDetails.name = nickNames.nickNameOrName(plc.senderDetails)
            plc
        }
        it
    }

    private fun MutableLiveData<MutableList<PodiumLiveChat>>.addAndPost(item: PodiumLiveChat) {
        var list = this.value?.apply {
            add(0, item)
        }.orEmpty()
        if (list.size > 1000) {
            list = list.slice(0..999)
        }
        this.postValue(list.toMutableList())
    }

    var chatText = MutableLiveData("")

    fun clearChatText() {
        chatText.postValue("")
    }

    enum class ChatDisableReason {
        DISABLED_BY_MANAGER,
        DISABLED_BY_ADMIN,
        CHALLENGE_RUNNING,
        UNKNOWN
    }

    val isChatFrozen = MutableLiveData<Pair<Boolean?, Boolean?>>(Pair(false, null))
    val chatDisabled: LiveData<ChatDisableReason?> by lazy {
        val med = MediatorLiveData<ChatDisableReason?>()
        fun update() {
            val pod = _podium.value
            val challenge = activeChallenge.value

            fun isChallengeRunning(): Boolean {
                if (challenge != null) {
                    return when (challenge.challengeType) {
                        ChallengeType.MAIDAN -> challenge.status == PodiumChallenge.ChallengeStatus.LIVE
                        else -> challenge.status != PodiumChallenge.ChallengeStatus.ENDED
                    }
                }
                return false
            }

            val reason: ChatDisableReason? = if (isChallengeRunning()) ChatDisableReason.CHALLENGE_RUNNING
            else if (pod?.chatDisabled==true) {
                when(pod.chatDisabledByRole) {
                    Podium.PodiumUserRole.MANAGER -> ChatDisableReason.DISABLED_BY_MANAGER
                    Podium.PodiumUserRole.ADMIN -> ChatDisableReason.DISABLED_BY_ADMIN
                    else -> ChatDisableReason.UNKNOWN
                }
            } else null
            reason?.let {
                if (iAmManager.value!=true) {
                    clearChatText()
                }
            }
            med.postValue(reason)
        }
        med.addSource(_podium) { update() }
        med.addSource(activeChallenge) { update() }
        med.distinctUntilChanged()
    }

    val onChatDisabled = LiveEvent<Pair<Boolean?, Podium.PodiumUserRole?>>()
    val onAdminMuted = LiveEvent<Pair<Boolean?, Boolean?>>()
    val onLikesDisabled = LiveEvent<Pair<Boolean?,Podium.PodiumUserRole?>>()
    val onMicEnabled = LiveEvent<Pair<Boolean?,Podium.PodiumUserRole?>>()
    val onGiftPaused = LiveEvent<Boolean?>()

    private var debugReportTriggered = false

    private fun createDummyChatMessage(message: String) = PodiumLiveChat(
        podiumId = _podiumId.value?: "0",
        chatId = UuidCreator.getTimeBased().toString(),
        message = message,
        created = DateTimeUtils.getZonedDateTimeNowAsString(),
        senderDetails = SenderDetails.from(accountRepo.user),
        userId = accountRepo.user.id,
        countryCode = userCountryCode.value
    )

    private fun printDebugReport(message: String? = null) {
        if (!debugReportTriggered && message!=null) return
        debugReportTriggered = true
        Log.w("PLW", "startConnectionChecker : time since last sync: ${PodiumEventService.timeSinceLastSync?.seconds}")
        val report = "\n🟠🟠🟠🟠🟠 " +
                "\nAgora Session: ${if(agoraSession!=null)"Exists" else null}" +
                "\nAgora State: ${agoraSession?.connectionState()} (${agoraSession?.connectionStateInt()})" +
                "\nSocket Connected: ${podiumEventRepo.connected}" +
                "\nLast Sync: ${PodiumEventService.timeSinceLastSync?.let { DateTimeUtils.formatDuration(it) }}" +
                "\n🟠🟠🟠🟠🟠"
        val chat = createDummyChatMessage(message?:report)
        _liveChat.addAndPost(chat)
    }

    fun sendChatMessage() {
        sendChatMessage(PodiumLiveChatType.NORMAL,chatText.value.orEmpty(), UserStats(((user.userRating?:0.0)*100).roundToInt(),null,null))
    }

    private fun sendChatMessage(chatType: PodiumLiveChatType, message:String = "",userStats: UserStats? = null) {
        if(BuildConfig.BUILD_TYPE != Constants.BuildType.RELEASE.value) {
            if (chatText.value=="debugReport") {
                printDebugReport()
                clearChatText()
                return
            }
        }
        _podiumId.value?.let { id ->
            val payload = PodiumLiveChat(
                podiumId = id,
                chatId = UuidCreator.getTimeBased().toString(),
                message = message,
                created = DateTimeUtils.getZonedDateTimeNowAsString(),
                senderDetails = SenderDetails.from(accountRepo.user),
                userId = accountRepo.user.id,
                countryCode = userCountryCode.value,
                _chatType = chatType,
                userStats = userStats
            )
            podiumEventRepo.sendChatMessage(payload)
            toggleTheaterChatInput(false)
            clearChatText()
        }
    }

    fun deleteChatMessage(podiumId: String,chatId: String){
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val payload = PodiumCommentDeletePayload(
                    podiumId = podiumId,
                    chatId = chatId)
                podiumEventRepo.deletePodiumChatMessage(payload)
            } catch (e: Exception) {
            }
        }

    }
    val iAmManager = podium.map {
        it ?: return@map null
        return@map it.isManagerCompat
    }.distinctUntilChanged()

    val requestToSpeakDisabled = podium.map{
        it ?: return@map null
        return@map it.requestToSpeakDisabled
    }.distinctUntilChanged()

    val iAmElevated = podium.map {
        it ?: return@map null
        return@map it.role?.isElevated == true || it.isManagerCompat
    }.distinctUntilChanged()

    var invitedToBeAdmin = podium.map {
//        Log.d("PLVM" + _podiumId.value, "podium admin status: ${it?.id}")
        it ?: return@map null
        return@map it.invitedToBeAdmin
    }.distinctUntilChanged()

    val hasSpaceForNewSpeaker = _speakers.map {
        val maxSpeakers = podiumKind.value?.maxSpeakers?:0
        return@map it.size < maxSpeakers
    }.distinctUntilChanged()

    fun hasSpaceForNewSpeakerExcludingMainScreens(): Boolean {
        val kind = podiumKind.value?:return false
        val maxSpeakers = kind.maxSpeakers-kind.mainScreens
        return speakers.value.orEmpty().size<maxSpeakers
    }

    val canInvitePeople: LiveData<Boolean> = iAmElevated.map {
        it ?: return@map false
        it
    }.distinctUntilChanged()

    fun canReport(other: AbstractUser) = user.canReport(other)
    fun canBan(other: AbstractUser) = user.canBan(other)

    val meAsSpeaker = _speakers.map {
        it.find { it.speaker.id.isSelf() }
    }

    val iAmSpeaker = meAsSpeaker.map {
        it != null
    }.distinctUntilChanged()

    val iAmMinister:Boolean
        get() = user.citizenship == UserCitizenship.MINISTER
    val iAmPresident:Boolean
        get() = user.citizenship == UserCitizenship.PRESIDENT

    val privilegedCitizenship:Boolean
        get() = iAmMinister || iAmPresident

    val iHavePowerToEndPodium:Boolean
        get() = user.userEmpowerment?.allowEndPodium == true || privilegedCitizenship
    private val citizenshipCanHideLiveUsers = setOf(UserCitizenship.MINISTER, UserCitizenship.PRESIDENT)
    val liveUserAccessManager: Boolean
        get()= iAmManager.value == true && user.citizenship in citizenshipCanHideLiveUsers


    private val _myVideoIsTurnedOn = MutableLiveData(false)
    val myVideoIsTurnedOn: LiveData<Boolean> = _myVideoIsTurnedOn.distinctUntilChanged()
    val onMyVideoTurnedOn = LiveEvent<Boolean>()

    fun enableVideo(on: Boolean) {
        Log.w("AGREGS", "enableVideo: $on")
        _myVideoIsTurnedOn.postValue(on)
        onMyVideoTurnedOn.postValue(on)
    }

    fun switchCamera() {
        agoraSession?.switchCamera()
    }

    val showVideoToggle: LiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(true)
        fun update() {
            /*val canTurnOn  = mainScreenSpeaker.value?.speaker?.id.isSelf() */ /*&& mainScreenSpeaker.value?.speaker?.forceAllowCamera == true*/
            val blockedByChallenge = activeChallenge.value?.challengeType in setOf(ChallengeType.GIFTS,ChallengeType.CONFOUR,ChallengeType.PENALTY,ChallengeType.FLAGS,ChallengeType.BOXES)
            med.postValue(iAmSpeaker.value == true && !blockedByChallenge && !isAssemblyPodium)
        }
        med.addSource(iAmSpeaker) { update() }
        med.addSource(activeChallenge) { update() }
        med.distinctUntilChanged()
    }

    fun isMainScreenSpeaker(speaker: PodiumSpeaker): Boolean {
        return mainScreenSpeaker.value?.speaker?.id == speaker.id
    }
    fun iAmOnMainScreen(): Boolean {
        return mainScreenSpeakerId.value.isSelf() || secondMainScreenSpeakerId.value.isSelf()
    }
    val remoteVideoState = MutableLiveData<Map<Int,AgoraEngineService.VideoState>>(hashMapOf())

    val showLocalVideoSurface: LiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(true)
        fun update() {
            val msId = mainScreenSpeakerId.value
            val state = remoteVideoState.value.orEmpty().getOrDefault(msId, null)
            val show = if (msId==user.id) _myVideoIsTurnedOn.value==true
            else state == AgoraEngineService.VideoState.PLAYING
            med.postValue(show)
        }
        med.addSource(mainScreenSpeakerId) { update() }
        med.addSource(_myVideoIsTurnedOn) { update() }
        med.addSource(remoteVideoState) { update() }
        med.distinctUntilChanged()
    }

    val iAmMuted = meAsSpeaker.map {
        Log.d("PPF","iAmMuted: speaker: $it")
        it?.muted ?: true
    }.distinctUntilChanged()

    val hasOtherElevatedSpeakers: Boolean
        get() {
            return _speakers.value.orEmpty().any { it.speaker.id.notSelf() && it.speaker.role?.isElevated == true }
        }

    // ASSEMBLY
    val onAssemblyPausedSpeaking = LiveEvent<Long>()
    val iAmPaused = LiveEvent<Boolean>()
    val myTurnStarted = LiveEvent<Boolean>()

    val onAssemblySpeakingTimeExtended = LiveEvent<Boolean>()

    val speakerZoneActivated = _speakers.map {
        return@map it.size > 1
    }.distinctUntilChanged()

    val currentAssemblySpeaker = _speakers.map {
        if (podiumKind.value!=PodiumKind.ASSEMBLY) return@map null
        it.find { it.speaker.speakingStatus in listOf(AssemblySpeakingStatus.ACTIVE,AssemblySpeakingStatus.WAITING) }?.speaker
    }

    val isCurrentAssemblySpeaker = currentAssemblySpeaker.map {
        it?.id.isSelf()
    }

    var currentActiveSpeaker: Int? = null

    fun resetCurrentActiveSpeaker() {
        currentActiveSpeaker = null
    }

    val onManagerSkipEnabled = LiveEvent<Boolean>()

    val showSkipSpeaking: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            val iCanSkip = if (iAmManager.value == true) {
                isCurrentAssemblySpeaker.value == true || onManagerSkipEnabled.value == true
            } else {
                isCurrentAssemblySpeaker.value == true
            }
            med.postValue(iCanSkip && (_speakers.value?.size?:0) > 1)
        }
        med.addSource(_speakers) { update() }
        med.addSource(isCurrentAssemblySpeaker) { update() }
        med.addSource(onManagerSkipEnabled) { update() }
        med
    }

    val startTimerForAssemblySpeaking: MediatorLiveData<Long?> by lazy {
        val med = MediatorLiveData<Long?>()
        fun update() {
            val speaker = _speakers.value?.find { it.speaker.speakingStatus == AssemblySpeakingStatus.ACTIVE || it.speaker.speakingStatus == AssemblySpeakingStatus.WAITING }?.speaker
            val speakerZoneCunt = _speakers.value?.size?:0
            if (speakerZoneCunt <= 1) {
                onManagerSkipEnabled.postValue(false)
            }
            if (speaker != null && currentActiveSpeaker != speaker.id && speakerZoneCunt > 1) {
                Log.w("PLV", "startTimerForAssemblySpeaking")
                med.postValue(speaker.speakingTimeRemaining)
                currentActiveSpeaker = speaker.id
            }
        }
        med.addSource(_speakers) { update() }
        med
    }

    val hasPausedAssemblySpeaker: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun update() {
            med.postValue(_speakers.value?.find {it.assemblySpeakingStatus == AssemblySpeakingStatus.PAUSED} != null)
        }
        med.addSource(_speakers) { update() }
        med.addSource(onAssemblyPausedSpeaking) { update() }
        med
    }

    fun fetchAssemblySpeakingStatus(id : Int): AssemblySpeakingStatus? {
        return _speakers.value?.find { it.speaker.id == id}?.speaker?.speakingStatus
    }

    fun skipSpeaking() {
        val reason = AssemblyStopSpeakingReason.SKIP
        val skippedBy = if (isCurrentAssemblySpeaker.value == true) AssemblySpeakingSkippedBy.SELF else AssemblySpeakingSkippedBy.HOST
        val skipReason = if (isCurrentAssemblySpeaker.value == true) AssemblySpeakingSkipReason.SKIPPED_TURN else AssemblySpeakingSkipReason.SKIPPED_INACTIVE_SPEAKER
        stopSpeaking( reason, skippedBy, skipReason)
    }

    fun stopSpeaking(reason: AssemblyStopSpeakingReason, skippedBy: AssemblySpeakingSkippedBy? = null, skipReason: AssemblySpeakingSkipReason? = null) {
        val userId = currentAssemblySpeaker.value?.id?:return
        val pId = _podiumId.value ?: return
        if(user.id == userId) {
            agoraSession?.muteAudio(true)
        }
        podiumRepository.stopSpeaking(
            PodiumAssemblyStopSpeakingPayload(pId,
                                              userId, reason = reason, skippedBy, skipReason
            )
        )
        _speakers.value?.find { it.speaker.id == userId }?.apply {
            speaker.speakingStatus = AssemblySpeakingStatus.INACTIVE
            speaker.muted = true
            assemblySpeakingStatus = AssemblySpeakingStatus.INACTIVE
        }
        _speakers.postValue(_speakers.value)
        resetCurrentActiveSpeaker()
    }

    fun notifyAssemblySpeakingTimerValue(remainingMs: Long) {
        _speakers.value?.find { it.speaker.speakingStatus == AssemblySpeakingStatus.ACTIVE || it.speaker.speakingStatus == AssemblySpeakingStatus.WAITING || (it.speaker.isManager && hasPausedAssemblySpeaker.value == true) }?.apply {
            assemblyTimeRemaining = DateTimeUtils.formatSeconds(remainingMs)

        }
//        _speakers.postValue(_speakers.value)
    }

    fun clearAssemblySpeakingTimer() {
        _speakers.value?.find { it.speaker.speakingStatus == AssemblySpeakingStatus.ACTIVE || it.speaker.speakingStatus == AssemblySpeakingStatus.WAITING || (it.speaker.isManager && hasPausedAssemblySpeaker.value == true) }?.apply {
            assemblyTimeRemaining = ""
        }
        _speakers.postValue(_speakers.value)
    }

    fun extendSpeakingTime(userId: Int) {
        val pId = _podiumId.value ?: return
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (podiumRepository.extendSpeakingTime(pId, userId)) {
                    is ResultOf.Success -> {}
                    is ResultOf.APIError -> {}
                    is ResultOf.Error -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLCVM", "onExitChallenge: ${e.message}")
            }
        }
    }

    fun canEndSpeakingSession(speaker: PodiumSpeaker): Boolean {
        val hasPower = when(podiumKind.value) {
            PodiumKind.ALONE -> iAmManager.value==true
            PodiumKind.ASSEMBLY -> iAmManager.value==true
            PodiumKind.INTERVIEW -> iAmManager.value == true && !(iAmOnMainScreen() && speaker.id.isSelf())
            PodiumKind.THEATER -> iAmManager.value == true && isMainScreenSpeaker(speaker) && !speaker.id.isSelf()
            else -> iAmElevated.value == true
        }
        Log.w("PLVM", "canEndSpeakingSession: $hasPower || ${speaker.id} == ${user.id} || $empoweredToEndSpeakingSession")
        return (hasPower || speaker.id.isSelf() || empoweredToEndSpeakingSession) && !isManager(speaker.id) && challengeRunning.value!=true
    }

    val empoweredToEndSpeakingSession: Boolean
        get()= user.userEmpowerment?.allowEndSpeakingSessionPodium?:false


    fun canExitPodium(speaker: PodiumSpeaker): Boolean {
        return if (speaker.id.isSelf()) return iCanExitPodium() else false
    }

    fun iCanExitPodium(): Boolean {
        if (iAmPartOfRunningChallenge.value==true) return false
        return true
    }

    fun canShowInMainScreen(speaker: PodiumSpeaker): Boolean {
        return !isMainScreenSpeaker(speaker) && challengeRunning.value!=true && podiumKind.value == PodiumKind.LECTURE
    }

    fun isManager(id: Int): Boolean {
        return id == podium.value?.managerId
    }

    fun canShowInviteToSpeak(senderDetails: SenderDetails) : Boolean {
        if (podiumKind.value==PodiumKind.ALONE) return false
        val eligibleToJoin = true /** when(podiumKind.value) {
            PodiumKind.INTERVIEW -> true
            else -> senderDetails.premium || senderDetails.citizenship in UserCitizenship.allExceptVisitor()
        }**/
        val hasSpace = hasSpaceForNewSpeaker.value == true
        return eligibleToJoin && hasSpace && podium.value?.requestToSpeakDisabled == false && challengeActive.value!=true && getSpeaker(senderDetails.id) == null && iAmManager.value == true
    }

    val isWaitingToSpeak: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            med.postValue(_waiters.value?.find { it.id.isSelf() } != null && podiumDetailsLoading.value == false)
        }
        med.addSource(_waiters) { update() }
        med.addSource(podiumDetailsLoading) { update() }
        med
    }

    val showRequestToSpeak: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            Log.i("triggerSpeakDisable", "update: ${podium.value?.requestToSpeakDisabled}")
            val iCanRequest = if (iAmManager.value == true) true
            else podium.value?.requestToSpeakDisabled == false
            med.postValue(
                iCanRequest &&
                iAmSpeaker.value==false &&
                isWaitingToSpeak.value == false &&
                podiumDetailsLoading.value == false &&
                challengeActive.value!=true
            )
        }
        med.addSource(iAmManager) { update() }
        med.addSource(iAmSpeaker) { update() }
        med.addSource(isWaitingToSpeak) { update() }
        med.addSource(podiumDetailsLoading) { update() }
        med.addSource(challengeActive) { update() }
        med.addSource(requestToSpeakDisabled) { update() }
        med
    }

    val canSendChats: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(true)
        fun update() {
            if (chatDisabled.value != ChatDisableReason.CHALLENGE_RUNNING && iAmManager.value == true) {
                med.postValue(true) // manager can send chat even after pause comments
                return
            }
            med.postValue(isChatFrozen.value?.first != true && chatDisabled.value == null)
        }
        med.addSource(isChatFrozen) { update() }
        med.addSource(chatDisabled) { update() }
        med
    }

    private suspend fun getPodiumDetails(id: String, softLoad: Boolean = false): Podium? {
        when (val result = podiumRepository.getPodiumDetails(id)) {
            is ResultOf.APIError -> {
                onPodiumLoadError.postValue(result.error.toString())
            }
            is ResultOf.Error -> {
                onPodiumLoadError.postValue(result.exception.toString())
            }
            is ResultOf.Success -> {
                _podium.postValue(result.value.copy(yallaChallengesCount = result.value.yallaChallengesCount, totalCoins = result.value.totalCoins ?: _podium.value?.totalCoins))
                _maidanCoinBalance.postValue(Pair(result.value.coinsGiven?:0,result.value.coinBalance?:0.0))
                Log.w("PCP", "getPodiumDetails: challenge status: ${result.value.challenge?.status}")
                if (!softLoad) postChallenge(result.value.challenge)
                return result.value
            }
        }
        return null
    }

    val waitListLoading = MutableLiveData(false)

    private suspend fun getPodiumWaitList(id: String) {
        waitListLoading.postValue(true)
        when (val result = podiumRepository.getPodiumWaitList(id)) {
            is ResultOf.APIError -> {
            }

            is ResultOf.Error -> {
            }

            is ResultOf.Success -> {
                _waiters.postValue(result.value.toMutableList())
            }
        }
        waitListLoading.postValue(false)
    }

    var agoraSession: AgoraEngineService.AgoraSession? = null
        private set

    private val Podium.isManagerCompat: Boolean
        get() {
            return isManager || managerId.isSelf()
        }

    private var channelEventListener = object : AgoraEngineService.ChannelEventListener {
        override fun onJoinedChannel(elapsed: Int) {
            Log.w("AGORA", "onJoinedChannel: ")
            val pod = _podium.value ?: return
            viewModelScope.launch(Dispatchers.IO) {
                Log.d("PLW", "calling start")
                PodiumLiveWorker.endAllAndStart(pod.id, pod.name, pod.role.orDefault(), pod.wasInvited ?: false)
            }
        }

        override fun onVideoStateChanged(id: Int, state: AgoraEngineService.VideoState) {
            Log.w("AGORA", "onVideoStateChanged: $id $state")
//            if (id == mainScreenSpeakerId.value && id!=user.id) {
                val map = remoteVideoState.value.orEmpty().toMutableMap()
                map[id] = state
                remoteVideoState.postValue(map.toMap())
//            }
        }

        override fun onLeave() {
            viewModelScope.launch(Dispatchers.IO) {
                Log.d("PLW", "calling end")
                PodiumLiveWorker.endAll()
            }
        }

        override fun onLeaveTriggered(stack: String) {
            printDebugReport("🟥🟥🟥🟥🟥🟥🟥\n$stack")
        }

        override fun onError(code: Int) {
            printDebugReport("Agora Error $code")
        }

        override fun onConnectionStateChanged(state: String, reason: String) {
//            viewModelScope.launch(Dispatchers.Main) {

//                if (BuildConfig.BUILD_TYPE != com.app.messej.data.Constants.BuildType.RELEASE.value) {
//                    _liveChat.addAndPost(getDebugChat("Agora State Change: \nState: $stateStr \nReason: $reasonStr"))
//                }
//            }
        }
    }

//    private fun getDebugChat(message: String): PodiumLiveChat {
//        return PodiumLiveChat(
//            podiumId = _podiumId.value?:"0",
//            chatId = UuidCreator.getTimeBased().toString(),
//            message = message,
//            created = DateTimeUtils.getZonedDateTimeNowAsString(),
//            senderDetails = SenderDetails.from(user),
//            userId = user.id,
//            countryCode = user.countryCode
//            )
//    }

    fun joinChannel() {
        val pod = _podium.value ?: return
        val token = agoraToken ?: return
//        if (BuildConfig.BUILD_TYPE != com.app.messej.data.Constants.BuildType.RELEASE.value) {
//            _liveChat.addAndPost(getDebugChat("Joining Channel ${pod.id} with token $token"))
//        }
        Log.w("AGORA", "joinChannel: ${pod.agoraChannelID}")
        viewModelScope.launch(Dispatchers.IO) {
            agoraSession = null
            printDebugReport("\n🐞 Checking Agora Session")
            podiumRepository.checkExistingAgoraSession(pod.agoraChannelID)?.also { existingSession ->
                printDebugReport("\n🐞 Retreived existing Agora Session")
                agoraSession = existingSession
//                if (BuildConfig.BUILD_TYPE != com.app.messej.data.Constants.BuildType.RELEASE.value) {
//                    _liveChat.addAndPost(getDebugChat("Existing Session: $agoraSession"))
//                }
                existingSession.registerListener(channelEventListener)
            } ?: run {
                val result = podiumRepository.joinAgoraSession(pod.agoraChannelID, pod.kind.orDefault(), token, channelEventListener)
                Log.w("AGORA", "joinChannel: $result")

                printDebugReport("\n🐞 Creating new Agora Session")
                if (result is ResultOf.Success) {
//                    if (BuildConfig.BUILD_TYPE != com.app.messej.data.Constants.BuildType.RELEASE.value) {
//                        _liveChat.addAndPost(getDebugChat("Joined Channel ${pod.id} successfully"))
//                    }
                    agoraSession = result.value
                    printDebugReport("\n🐞 Success")
                } else {
                    printDebugReport("\n🐞 Error Creating Session: \n ${result.errorMessage()}")
                }
            }
            agoraSession?.apply {
                muteAudio(meAsSpeaker.value?.muted ?: true)
                if (pod.isManagerCompat) {
                    setAsSpeaker(true)
//                    onReadyToStreamVideo.postValue(Unit)
                    // May not be needed anymore
//                    getPodiumDetails(pod.id)?.let { pod ->
//                        _speakers.mapAndPost(pod.speakers.toMutableList())
//                    }
                }
            }
        }
    }

    fun setupMainScreenPreview(surface: SurfaceView) {
        agoraSession?.attachLocalVideoStream(surface)
    }

    fun detachMainScreenPreview() {
        agoraSession?.detachLocalVideoStream()
    }

    fun setupRemoteMainScreenPreview(surface: SurfaceView, id: Int) {
        Log.w("AGORA", "setupRemoteMainScreenPreview: ${id}")
        viewModelScope.launch {
            agoraSession?.attachRemoteVideoStream(id, surface)
        }
    }

    fun checkIfStillLive() {
        Log.w("PodiumLVM", "checkIfStillLive")
        // If this is null, the VM is just getting set up. So executing this check can interfere with the Go-Live API
        agoraSession?:return

        viewModelScope.launch {
//            if (agoraSession?.connected() == false) {
                Log.w("PodiumLVM", "previous kick: $previousKickReason")
                previousKickReason?.let {
                    kickFromPodium(it,true)
                    return@launch
                }
                val id = _podiumId.value ?: return@launch
                podiumDetailsLoading.postValue(true)
                val pod = getPodiumDetails(id) ?: return@launch
                podiumDetailsLoading.postValue(false)
                Log.w("PodiumLVM", "podium live? ${pod.isLive}")
                if (!pod.isLive) kickFromPodium(PodiumKickReason.NotLive)
//            }
        }
    }

    val onRequestedToSpeak = LiveEvent<Boolean>()
    val onRequestedToSpeakError = LiveEvent<String?>()

    fun requestToSpeak() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
//                val speaker = PodiumSpeaker.from(user)
                when (val result = podiumRepository.requestToSpeak(podiumId.value?: return@launch)) {
                    is ResultOf.Success -> {
//                        withContext(Dispatchers.Main) {
//                            _waiters.addAndPost(speaker)
//                        }
                        onRequestedToSpeak.postValue(true)
                    }

                    is ResultOf.Error -> {

                    }

                    is ResultOf.APIError -> {
                        onRequestedToSpeakError.postValue(result.error.message)
                    }
                }
            } catch (e: Exception) {
                Log.d("PodiumLVM", "requestToSpeak: error: ${e.message}")
            }
        }
    }

    val showWaitListCompactLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(false)
        fun update() {
            med.postValue(cancelSpeakRequestLoading.value == true || allowToSpeakLoading.value == true || userBlockLoading.value == true)
        }
        med.addSource(cancelSpeakRequestLoading) { update() }
        med.addSource(allowToSpeakLoading) { update() }
        med.addSource(userBlockLoading) { update() }
        med
    }

    val cancelSpeakRequestLoading = MutableLiveData(false)
    val onRequestToSpeakCancelled = LiveEvent<String?>()

    fun cancelRequestToSpeak() {
        cancelSpeakRequestLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.cancelRequestToSpeak(podiumId.value!!)) {
                    is ResultOf.Success -> {
                        withContext(Dispatchers.Main) {
                            _waiters.removeAndPost(user.id)
                        }
                        onRequestToSpeakCancelled.postValue(result.value)
                    }

                    is ResultOf.APIError -> {
                        if (canShowAPIErrorMessage(result.code)) {
                            onRequestToSpeakCancelled.postValue(result.error.message)
                        }
                        Log.w("PodiumLVM", "allowToSpeak: $result")
                    }

                    is ResultOf.Error -> {
                        Log.w("PodiumLVM", "allowToSpeak: $result")
                    }
                }
            } catch (e: Exception) {
                Log.d("PodiumLVM", "cancelRequestToSpeak: error: ${e.message}")
            }
            cancelSpeakRequestLoading.postValue(false)
        }
    }

    val allowToSpeakLoading = MutableLiveData(false)
    val onUserAllowedToSpeak = LiveEvent<String?>()
    val onAllowedUserInsufficientBalanceToSpeak = LiveEvent<String>()

    fun allowToSpeak(speaker: PodiumSpeaker) {
        val pod = _podiumId.value ?: return
        allowToSpeakLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.allowUserToSpeak(pod, speaker)) {
                    is ResultOf.Success -> {
                        Log.w("PodiumLVM", "allowToSpeak: $result")
                        withContext(Dispatchers.Main) {
                            _waiters.removeAndPost(speaker.id)
                            _speakers.addAndPostSpeaker(speaker)
                            onUserAllowedToSpeak.postValue(result.value)
                        }
                    }

                    is ResultOf.APIError -> {
                        if (canShowAPIErrorMessage(result.code)) {
                            onUserAllowedToSpeak.postValue(result.error.message)
                        } else if (result.code == 402) {
                            onAllowedUserInsufficientBalanceToSpeak.postValue(speaker.name)
                        }
                        Log.w("PodiumLVM", "allowToSpeak: $result")
                    }

                    is ResultOf.Error -> {
                        Log.w("PodiumLVM", "allowToSpeak: $result")
                    }
                }

            } catch (e: Exception) {
                Log.d("PodiumLVM", "allowToSpeak: error: ${e.message}")
            }
            allowToSpeakLoading.postValue(false)
        }
    }

    private val _leavingPodium = MutableLiveData<Boolean>(false)
    val leavingPodium: LiveData<Boolean> = _leavingPodium

    val onLeftPodium = LiveEvent<Unit>()

    fun leavePodium(podiumId: String, onLeft: ((Boolean) -> Unit)? = null) {
        _leavingPodium.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                Log.d("PodiumLVM", "trying to leave")
                when (val result = podiumRepository.leavePodium(podiumId)) {
                    is ResultOf.Success -> {
                        Log.d("PodiumLVM", "leave success")
                        agoraSession?.leave()
                        viewModelScope.launch(Dispatchers.Main) {
                            onLeft?.invoke(true)
                        }
                    }

                    is ResultOf.APIError -> {
                        viewModelScope.launch(Dispatchers.Main) {
                            onLeft?.invoke(false)
                        }
                    }

                    is ResultOf.Error -> {
                        viewModelScope.launch(Dispatchers.Main) {
                            onLeft?.invoke(false)
                        }
                    }
                }

            } catch (e: Exception) {
                viewModelScope.launch(Dispatchers.Main) {
                    onLeft?.invoke(false)
                }
                Log.d("PodiumLVM", "leave: error: ${e.message}")
            }
            _leavingPodium.postValue(false)
        }
    }

    fun leave(onLeft: ((Boolean) -> Unit)? = null) {
        val pod = _podiumId.value ?: return
        leavePodium(pod,onLeft?: { left ->
            if (left) onLeftPodium.postValue(Unit)
        })
    }

    val onExitPodium = LiveEvent<Boolean>()

    fun exit() {
        val pod = _podiumId.value ?: return
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.exitPodium(pod)) {
                    is ResultOf.Success -> {
                        agoraSession?.leave()
                        onExitPodium.postValue(true)
                    }

                    is ResultOf.APIError -> {}
                    is ResultOf.Error -> {}
                }

            } catch (e: Exception) {
                Log.d("PodiumLVM", "exit: error: ${e.message}")
            }
        }
    }

    private val _closingPodium = MutableLiveData<Boolean>(false)
    val closingPodium: LiveData<Boolean> = _closingPodium

    fun close(onClosed: (()->Unit) = {
        kickFromPodium(PodiumKickReason.PodiumClosedByMe)
    }) {
        val pod = _podiumId.value ?: return
        _closingPodium.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.closePodium(pod)) {
                    is ResultOf.Success -> {
                        agoraSession?.leave()
                        viewModelScope.launch(Dispatchers.Main) {
                            onClosed.invoke()
                        }
                    }

                    is ResultOf.APIError -> {
                        // TODO handle error
                    }

                    is ResultOf.Error -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLVM", "close: error: ${e.message}")
            }
            _closingPodium.postValue(false)
        }
    }

    fun sendPodiumGift(podiumGiftPayload: SentGiftPayload?) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                podiumEventRepo.sendGift(podiumGiftPayload!!)
            } catch (e: Exception) {
            }
        }
    }

    val userBlockLoading = MutableLiveData(false)
    val userBlockLoadingSource = MutableLiveData<PodiumBlockFrom?>(null)
    val onUserBlockToggled = LiveEvent<String?>()

    fun toggleUserBlock(userId: Int, action: BlockUnblockAction, from: PodiumBlockFrom? = null) {
        val pod = _podiumId.value ?: return
        userBlockLoadingSource.postValue(from)
        userBlockLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.blockUnblockPodiumParticipant(pod, userId, action,from)) {
                    is ResultOf.Success -> {
                        onUserBlockToggled.postValue(result.value)
                        getSpeaker(userId)?.let { sp ->
                            when(from) {
                                PodiumBlockFrom.AUDIENCE -> sp.blockedFromAudience = action == BlockUnblockAction.BLOCK
                                PodiumBlockFrom.STAGE -> sp.blockedFromStage = action == BlockUnblockAction.BLOCK
                                null -> sp.blockedFromPodium = action == BlockUnblockAction.BLOCK
                            }
                        }
                    }
                    is ResultOf.APIError -> {
                        onUserBlockToggled.postValue(result.error.message)
                    }
                    is ResultOf.Error -> {}
                }

            } catch (e: Exception) {
                Log.d("PodiumLVM", "sendPodiumInvitation: error: ${e.message}")
            }
            userBlockLoading.postValue(false)
        }
    }

    val userFreezeLoading = MutableLiveData(false)
    val onUserFreezeToggled = LiveEvent<String?>()

    fun freezeToggle(userId: Int, name: String, freeze: Boolean = true) {
        val pod = _podiumId.value ?: return
        userFreezeLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.freezeToggle(pod, userId, freeze)) {
                    is ResultOf.Success -> {
                        onUserFreezeToggled.postValue(result.value)
                    }

                    is ResultOf.APIError -> {
                        if (canShowAPIErrorMessage(result.code)) {
                            onUserFreezeToggled.postValue(result.error.message)
                        }
                    }

                    is ResultOf.Error -> {}
                }

            } catch (e: Exception) {
                Log.d("PodiumLVM", "sendPodiumInvitation: error: ${e.message}")
            }
            userFreezeLoading.postValue(false)
        }
    }

    val muteToggleLoading = MutableLiveData(false)
    val onUserMuteToggled = LiveEvent<String?>()

    fun muteToggleSelf() {
        _speakers.value?.find { it.speaker.id.isSelf() }?.speaker?.let { sp ->
            muteToggle(sp)
        }
    }

    fun muteToggle(speaker: PodiumSpeaker) {
        val pod = _podiumId.value ?: return
        val mutedBefore = speaker.muted
        if(isAssemblyPodium && iAmManager.value == false && !mutedBefore) return
        if(activeChallenge.value?.challengeType == ChallengeType.FLAGS && mutedBefore) return
        Log.d("PodiumLVM", "muteToggle: $mutedBefore to ${!mutedBefore}")
        muteToggleLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = if (speaker.id.isSelf()) podiumRepository.muteUnMuteSelf(pod, mutedBefore) else podiumRepository.muteUnMuteUser(pod, speaker)) {
                    is ResultOf.Success -> {
                        _speakers.value?.find { it.speaker.id == speaker.id }?.apply {
                            muted = !mutedBefore
                            Log.d("PodiumLVM", "muteToggle: $muted")
                            if (speaker.id.isSelf()) {
                                agoraSession?.muteAudio(!mutedBefore)
                                if (podiumKind.value == PodiumKind.ASSEMBLY && speaker.speakingStatus == AssemblySpeakingStatus.WAITING && speakerZoneActivated.value == true) {
                                    if (!muted) {
                                        Log.w("PLV", "muteToglle: Force Active")
                                        speaker.speakingStatus = AssemblySpeakingStatus.ACTIVE
                                        assemblySpeakingStatus = AssemblySpeakingStatus.ACTIVE
                                    }
                                    _speakers.postValue(_speakers.value)
                                }
                            }
                            onUserMuteToggled.postValue(result.value)
                        }
                    }

                    is ResultOf.APIError -> {
                        if (canShowAPIErrorMessage(result.code)) {
                            onUserMuteToggled.postValue(result.error.message)
                        }
                    }

                    is ResultOf.Error -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLVM", "sendPodiumInvitation: error: ${e.message}")
            }
            muteToggleLoading.postValue(false)
        }
    }

    val endSpeakingSessionLoading = MutableLiveData(false)
    val onSpeakingSessionEnd = LiveEvent<String?>()

    fun endMySpeakingSession() {
        endSpeakingSession(PodiumSpeaker.from(user))
    }

    fun endSpeakingSession(speaker: PodiumSpeaker) {
        val pod = _podiumId.value ?: return
        endSpeakingSessionLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = if (speaker.id.isSelf()) podiumRepository.endSelfSpeakingSession(pod) else podiumRepository.endParticipantSpeakingSession(pod, speaker.id)) {
                    is ResultOf.Success -> {
                        withContext(Dispatchers.Main) {
                            _speakers.removeAndPostSpeaker(speaker.id)
                            onSpeakingSessionEnd.postValue(result.value)
                        }
                    }

                    is ResultOf.APIError -> {
                        if (canShowAPIErrorMessage(result.code)) {
                            onSpeakingSessionEnd.postValue(result.error.message)
                        }
                    }

                    is ResultOf.Error -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLVM", "endParticipantSpeakingSession: error: ${e.message}")
            }
            endSpeakingSessionLoading.postValue(false)
        }
    }

    private var liveTimerJob: Job? = null

    private val _liveTimer = MutableLiveData("")
    val liveTimer: LiveData<String> = _liveTimer

    private fun stopLiveTimer() {
        liveTimerJob?.apply {
            cancel()
            liveTimerJob = null
        }
    }

    private fun startLiveTimer() {
        stopLiveTimer()
        val startTime = podium.value?.parsedLastGoLiveTime ?: ZonedDateTime.now()
        liveTimerJob = viewModelScope.launch {
            try {
                while (true) {
                    val durationToNow = DateTimeUtils.durationToNowFromPast(startTime) ?: return@launch
                    val timeElapsed = DateTimeUtils.formatDuration(durationToNow)
                    _liveTimer.postValue(timeElapsed)
                    delay(1000)
                }
            } catch (e: Exception) {
                Log.e("PSLT", "startLiveTimer: failed", e)
            } finally {
                Log.w("PSLT", "live timer cancelled")
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        stopLiveTimer()
        agoraSession?.unregisterListener(channelEventListener)
    }

    val onLikedByParticipant = LiveEvent<Boolean>()
    val onLikedByHost = LiveEvent<Boolean>()

    fun likePodium() {
        _podiumId.value?.let {
            viewModelScope.launch {
                if(podiumRepository.likePodium(it)) {
                    onLikedByHost.postValue(true)
                    sendChatMessage(PodiumLiveChatType.PAID_LIKE)
                }
            }
        }
    }
    private val _isLikeLoading = MutableLiveData<Boolean>(false)
    val isLikeLoading: LiveData<Boolean> = _isLikeLoading

    val onPaidLikeInsufficientBalance = LiveEvent<Boolean>()
    val onPaidLikeError = LiveEvent<String>()

    fun sendPaidLike() {
        _podium.value?.let {
            viewModelScope.launch(Dispatchers.IO) {
                _isLikeLoading.postValue(true)
                try {
                    when (val result = podiumRepository.sendPaidLike(PaidLikePayload(likes = 1, receiverId = it.managerId, source = "podium", sourceId = it.id ))) {
                        is ResultOf.Success -> {
                            onLikedByParticipant.postValue(true)
                            sendChatMessage(PodiumLiveChatType.PAID_LIKE)
                        }
                        is ResultOf.APIError -> {
                            if (result.code == 400) {
                                onPaidLikeInsufficientBalance.postValue(true)
                            } else {
                                onPaidLikeError.postValue(result.error.message)
                            }
                        }
                        is ResultOf.Error -> {
                            onPaidLikeError.postValue(result.errorMessage())
                        }
                    }
                    _isLikeLoading.postValue(false)

                } catch (e: Exception) {
                    Log.d("PodiumLVM", "sendLikeByOthers: erPodiumActionTyperor: ${e.message}")
                }
            }
        }
    }


    val likeButtonEnabled: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()

        fun update() {
            val isLoading = _isLikeLoading.value ?: false
            med.postValue( _podium.value?.likesDisabled ?:false && !isLoading)
        }

        med.addSource(_podium) { update() }
        med.addSource(_isLikeLoading) { update() }
        med

    }


    val adminListActionLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            med.postValue(
                adminsListLoading.value == true || cancelAdminInviteLoading.value == true || dismissAsAdminLoading.value == true || appointAsAdminLoading.value == true
            )
        }
        med.addSource(adminsListLoading) { update() }
        med.addSource(cancelAdminInviteLoading) { update() }
        med.addSource(dismissAsAdminLoading) { update() }
        med.addSource(appointAsAdminLoading) { update() }
        med
    }

    val adminsListLoading = MutableLiveData(false)

    private val _allAdmins = podiumId.switchMap {
        it ?: return@switchMap null
        podiumRepository.getPodiumAdminsListPager(it).liveData.cachedIn(viewModelScope)
    }
    val allAdmins = _allAdmins.map {
        it.map { ps ->
            if (ps.invitedToBeAdmin) {
                if (!_invitedAdminIdList.contains(ps.id)) _invitedAdminIdList.add(ps.id)
            }
            else if (_invitedAdminIdList.contains(ps.id)) _invitedAdminIdList.remove(ps.id)
            ps.copy(
                name = nickNames.nickNameOrName(ps)
            )
        }
    }

    var _adminIdList: MutableList<Int> = mutableListOf()

    var _userPauseGiftList: List<UserGiftPaused> = listOf()


    fun isUserGiftPaused(userId: Int): Boolean {
        return _userPauseGiftList.find { it.giftPausedUser == userId }?.let { true } ?: false
    }

    private fun isGiftPausedByManager(userId: Int): Boolean {
        return _userPauseGiftList.any { it.giftPausedUser == userId && it.giftPausedBy == podium.value?.managerId }
    }

    private fun isGiftPausedByMe(userId: Int): Boolean {
        return _userPauseGiftList.any { it.giftPausedUser == userId && it.giftPausedBy.isSelf() }
    }

    private fun isGiftPausedBySelf(userId: Int): Boolean {
        return _userPauseGiftList.any { it.giftPausedUser == userId && it.giftPausedBy == userId }
    }

    fun isAdmin(id: Int) = _adminIdList.contains(id) && !isManager(id)
    fun isAdmin(speaker: PodiumSpeaker) = speaker.role == Podium.PodiumUserRole.ADMIN || isAdmin(speaker.id)


    fun isManagerAvailable(): Boolean {
        return _speakers.value?.any { it.speaker.role == Podium.PodiumUserRole.MANAGER } == true
    }

    fun shouldShowPauseGift(speakerId: Int, isVisitor: Boolean,isGolden:Boolean): Boolean {
        val showPauseForUser = if(isUserGiftPaused(speakerId)) isGiftPausedByMe(speakerId)
        else {
            iAmManager.value==true || speakerId.isSelf()
        }
        return podium.value?.podiumGiftPaused==false && showPauseForUser && getSpeaker(speakerId)?.inactive!=true && !isVisitor && !isGolden
    }



    fun isAdminCountReachLimit():Boolean  {
        return _adminIdList.size>PodiumChallenge.MAX_ADMINS
    }

    private var _invitedAdminIdList: MutableList<Int> = mutableListOf()

    fun isInvitedToBeAdmin(id: Int) = _invitedAdminIdList.contains(id)

    fun canAppointAsAdmin(id: Int, premium: Boolean?): Boolean {
        return iAmManager.value == true && id.notSelf() && premium == true && !isAdmin(id) && !isInvitedToBeAdmin(id) && !isManager(id) && podiumKind.value != PodiumKind.ASSEMBLY
    }
    fun canDismissAdmin(id: Int): Boolean {
        return iAmManager.value == true && id.notSelf() && isAdmin(id) && !isManager(id) && podiumKind.value != PodiumKind.ASSEMBLY
    }
    fun canWithdrawAsAdmin(id: Int): Boolean {
        return id.isSelf() && isAdmin(id) && !isManager(id)
    }
    fun canCancelAdminInvite(id: Int): Boolean {
        return iAmManager.value == true && id.notSelf() && isInvitedToBeAdmin(id) && !isManager(id)
    }
    fun canBlock(id: Int): Boolean {
        val hasPower = when(podiumKind.value) {
            PodiumKind.ALONE -> iAmManager.value==true
            PodiumKind.INTERVIEW -> iAmManager.value == true
            else -> iAmElevated.value == true
        }
        return hasPower && id.notSelf() && !isManager(id) && challengeRunning.value!=true && getSpeaker(id)?.citizenship != UserCitizenship.PRESIDENT
    }
    fun canFreeze(id: Int): Boolean {
        return iAmElevated.value == true && id.notSelf() && !isManager(id)
    }

    private val canSendFlax: Boolean
        get() = user.premium || !user.inactive

    private val canSendGifts: Boolean
        get() = !user.inactive

    fun <T>canSendGiftsTo(speaker: T): Boolean where T : AbstractUser, T : UserRatingProvider {
        val isGiftChallengePaused = challengeActive.value==true && activeChallenge.value?.challengeType==ChallengeType.GIFTS
        return speaker.id.notSelf() && !speaker.inactive && (if(isGiftChallengePaused) true else !isUserGiftPaused(speaker.id)) && (if(isGiftChallengePaused) true else  podium.value?.podiumGiftPaused!=true) && canSendGifts && !isVisitorUser && speaker.citizenship?.isVisitor != true  && speaker.citizenship?.isGolden != true
    }

    fun <T>canSendFlaxTo(speaker: T): Boolean where T : AbstractUser, T : UserRatingProvider {
        if (podiumKind.value==PodiumKind.THEATER) return false
        return speaker.id.notSelf() && !speaker.inactive && speaker.citizenship in UserCitizenship.allExceptVisitor() && canSendFlax && !isVisitorUser
    }

    fun canShowTheaterCharges(): Boolean {
        return iAmManager.value == true || isAdmin(user.id)
    }

    val isCoinCountVisible = _podium.map {
        when(it?.kind) {
            PodiumKind.THEATER, PodiumKind.ASSEMBLY, PodiumKind.LECTURE -> true
            else -> false
        }
    }

    private fun getPodiumAdminsList() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                _podiumId.value ?: return@launch
                adminsListLoading.postValue(true)
                when (val result = podiumRepository.getPodiumAdminsList(_podiumId.value!!)) {
                    is ResultOf.APIError -> {
                    }

                    is ResultOf.Error -> {
                    }

                    is ResultOf.Success -> {

                        result.value.toMutableList().let {
                            _invitedAdminIdList = it.filter { adminList ->
                                adminList.invitedToBeAdmin
                            }.map { flList -> flList.id }.toMutableList()
                        }

                    }
                }
                adminsListLoading.postValue(false)
            } catch (e: Exception) {
                Log.d("PodiumLVM", "getPodiumAdminsList: error: ${e.message}")
            }
        }
    }

    val onAdminActionFinished = LiveEvent<Boolean>()
    val appointAsAdminLoading = MutableLiveData(false)
    fun appointAsAdmin(userId: Int) {
        _podiumId.value?.let {
            appointAsAdminLoading.postValue(true)
            viewModelScope.launch(Dispatchers.IO) {
                try {
                    when (val result = podiumRepository.appointAnAdmin(it, userId)) {
                        is ResultOf.Success -> {
                            onAdminActionFinished.postValue(true)
                            _invitedAdminIdList.add(userId)
                        }

                        is ResultOf.APIError -> {}
                        is ResultOf.Error -> {}
                    }

                } catch (e: Exception) {
                    Log.d("PodiumLVM", "appointAsAdmin: erPodiumActionTyperor: ${e.message}")
                }
                appointAsAdminLoading.postValue(false)
            }
        }
    }

    val dismissAsAdminLoading = MutableLiveData(false)
    fun dismissAsAdmin(userId: Int) {
        _podiumId.value?.let {
            dismissAsAdminLoading.postValue(true)
            viewModelScope.launch(Dispatchers.IO) {
                try {
                    when (val result = podiumRepository.dismissAnAdmin(it, userId)) {
                        is ResultOf.Success -> {
                            onAdminActionFinished.postValue(true)
                            _adminIdList.removeIf { userId == it }
                        }

                        is ResultOf.APIError -> {}
                        is ResultOf.Error -> {}
                    }

                } catch (e: Exception) {
                    Log.d("PodiumLVM", "dismissAsAdmin: erPodiumActionTyperor: ${e.message}")
                }
                dismissAsAdminLoading.postValue(false)
            }
        }
    }

    val cancelAdminInviteLoading = MutableLiveData(false)
    fun cancelAdminInvite(userId: Int) {
        _podiumId.value?.let {
            cancelAdminInviteLoading.postValue(true)
            viewModelScope.launch(Dispatchers.IO) {
                try {
                    when (podiumRepository.cancelAdminAppoint(it, userId)) {
                        is ResultOf.Success -> {
                            onAdminActionFinished.postValue(true)
                            _invitedAdminIdList.removeIf { userId == it }
                        }

                        is ResultOf.APIError -> {}
                        is ResultOf.Error -> {}
                    }

                } catch (e: Exception) {
                    Log.d("PodiumLVM", "cancelAdminInvite: erPodiumActionTyperor: ${e.message}")
                }
                cancelAdminInviteLoading.postValue(false)
            }
        }
    }

    val declineRequestToSpeakLoading = MutableLiveData(false)
    val onRequestToSpeakDeclined = LiveEvent<Boolean>()
    fun declineRequestToSpeak(userId: Int) {
        _podiumId.value?.let {
            declineRequestToSpeakLoading.postValue(true)
            viewModelScope.launch(Dispatchers.Default) {
                try {
                    when (podiumRepository.declineRequestToSpeak(it, userId)) {
                        is ResultOf.Success -> {
                            withContext(Dispatchers.Main) {
                                _waiters.removeAndPost(userId)
                                onRequestToSpeakDeclined.postValue(true)
                            }
                        }

                        is ResultOf.APIError -> {}
                        is ResultOf.Error -> {}
                    }

                } catch (e: Exception) {
                    Log.d("PodiumLVM", "declineRequestToSpeak: erPodiumActionTyperor: ${e.message}")
                }
                declineRequestToSpeakLoading.postValue(false)
            }
        }
    }

    private val _podiumActionLoading: MutableLiveData<PodiumActionType?> = MutableLiveData(null)
    val podiumActionLoading: LiveData<PodiumActionType?> = _podiumActionLoading

    private val _podiumInviteMaxAdminsError = MutableLiveData<String?>("")
    val podiumInviteMaxAdminsError: LiveData<String?> = _podiumInviteMaxAdminsError

    fun respondAdminInviteAction(action: PodiumActionType, onSuccess: () -> Unit = {}) {
        _podiumId.value?.let {
            _podiumActionLoading.postValue(action)
            viewModelScope.launch(Dispatchers.IO) {
                when (val result = podiumRepository.respondAdminInviteAction(it, accountRepo.user.id, action)) {
                    is ResultOf.APIError -> {
                        _podiumInviteMaxAdminsError.postValue(result.error.message)
                    }
                    is ResultOf.Error -> {}
                    is ResultOf.Success -> {
                        val role = if (action == PodiumActionType.CONFIRM_PODIUM_ADMIN_INVITE) Podium.PodiumUserRole.ADMIN else _podium.value?.role
                        _podium.postValue(_podium.value?.copy(invitedToBeAdmin = false, role = role))
                        onSuccess.invoke()
                    }
                }
                _podiumActionLoading.postValue(null)
            }
        }
    }

    private val _blockedUsersList = podiumId.switchMap {
        it?: return@switchMap null
        podiumRepository.getPodiumBlockedListPager(it).liveData.cachedIn(viewModelScope)
    }
    val blockedUsersList: MediatorLiveData<PagingData<PodiumSpeaker>?> by lazy {
        val med = MediatorLiveData<PagingData<PodiumSpeaker>?>()
        fun update() {
            val data = _blockedUsersList.value?.map {
                it.copy(
                    name = nickNames.nickNameOrName(it)
                )
            }
            med.postValue(data)
        }
        med.addSource(_blockedUsersList) { update() }
        med.addSource(nickNamesLiveData) { update() }
        med
    }

    val blockedUsersListLoading = MutableLiveData(false)

    private val _frozenUsersList = podiumId.switchMap {
        it?: return@switchMap null
        podiumRepository.getPodiumFrozenListPager(it).liveData.cachedIn(viewModelScope)
    }
    val frozenUsersList: MediatorLiveData<PagingData<PodiumSpeaker>?> by lazy {
        val med = MediatorLiveData<PagingData<PodiumSpeaker>?>()
        fun update() {
            val data = _frozenUsersList.value?.map {
                it.copy(
                    name = nickNames.nickNameOrName(it)
                )
            }
            med.postValue(data)
        }
        med.addSource(_frozenUsersList) { update() }
        med.addSource(nickNamesLiveData) { update() }
        med
    }

    val frozenUsersListLoading = MutableLiveData(false)

    fun toggleComments() {
        val id = _podiumId.value ?: return
        val chatDisabled = podium.value?.chatDisabled ?: false
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = podiumRepository.setPodiumSettings(id, disableChat = !chatDisabled)) {
                is ResultOf.Success -> {
                    val newPod = _podium.value?.copy(
                        chatDisabled = !chatDisabled
                    )
                    _podium.postValue(newPod)
                }

                else -> {}
            }
        }
    }


    fun disableLikes() {
        val id = _podiumId.value ?: return
        val likeDisabled = podium.value?.likesDisabled ?: false
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = podiumRepository.setPodiumSettings(id, disableLikes = !likeDisabled)) {
                is ResultOf.Success -> {
                    val newPod = _podium.value?.copy(
                        likesDisabled = !likeDisabled
                    )
                    _podium.postValue(newPod)
                }

                else -> {}
            }
        }
    }

    fun pauseGift(pauseGift: PodiumPauseGift) {
        val id = _podiumId.value ?: return
        val giftPaused = podium.value?.podiumGiftPaused ?: false
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = podiumRepository.setPausePodiumGift(id, pauseGift = pauseGift)) {
                is ResultOf.Success -> {
                    val newPod = _podium.value?.copy(
                        podiumGiftPaused = !giftPaused
                    )
                    _podium.postValue(newPod)
                }

                else -> {}
            }
        }
    }

    fun pauseUserGift(userId: Int, pauseGift: PodiumPauseGift){
        val id = _podiumId.value ?: return
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = podiumRepository.setPauseUserPodiumGift(podiumId = id, participantId = userId ,pauseGift = pauseGift)) {
                is ResultOf.Success -> {
//                    val newPod = _podium.value?.copy(
//                        podiumGiftPaused = !giftPaused
//                    )
//                    _podium.postValue(newPod)
                }

                else -> {}
            }
        }
    }

    fun disableMic() {
        val id = _podiumId.value ?: return
        val micDisabled = podium.value?.requestToSpeakDisabled ?: false
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = podiumRepository.setPodiumSettings(id, disableMic = !micDisabled)) {
                is ResultOf.Success -> {
                    val newPod = _podium.value?.copy(
                        requestToSpeakDisabled = !micDisabled
                    )
                    _podium.postValue(newPod)
                }

                else -> {}
            }
        }
    }

    val liveUsersListLoading = MutableLiveData(false)

    private val _liveUsersList = _podiumId.switchMap {
        Log.d("PLULDS", "switchmap live users for $it")
        it ?: return@switchMap null
        podiumRepository.getPodiumLiveUsersListingPager(it).liveData.cachedIn(viewModelScope)
    }

    val liveUsersList: MediatorLiveData<PagingData<PodiumParticipant>?> by lazy {
        val med = MediatorLiveData<PagingData<PodiumParticipant>?>()
        fun update() {
            val data = _liveUsersList.value?.map { speaker ->
                speaker.copy(
                    name = nickNames.nickNameOrName(speaker)
                )
            }
            med.postValue(data)
        }
        med.addSource(nickNamesLiveData) { update() }
        med.addSource(_liveUsersList) { update() }
        med
    }

    val showInMainScreenLoading = MutableLiveData(false)
    val onShowInMainScreen = LiveEvent<Int>()
    fun showInMainScreen(userId: Int) {
        _podiumId.value?.let {
            showInMainScreenLoading.postValue(true)
            viewModelScope.launch(Dispatchers.IO) {
                if (podiumRepository.showInMainScreen(it, userId)) {
                    _speakers.value?.let {
//                                it.forEach { sp -> sp.speaker.id == userId }? //TODO update value
                        _speakers.postValue(it)
                    }
                    onShowInMainScreen.postValue(userId)

                }
                showInMainScreenLoading.postValue(false)
            }
        }
    }

    fun podiumInviteMaxAdminsErrorReset() {
        _podiumInviteMaxAdminsError.postValue("")
    }

    val onPodiumGiftSent = LiveEvent<SentGiftPayload>()

//    CHALLENGES

    var lastChallenge: PodiumChallenge? = null
        private set

    private val _activeChallenge = MutableLiveData<PodiumChallenge?>(null)
    val activeChallenge: LiveData<PodiumChallenge?> = _activeChallenge.map {
        Log.w("FEECHECK", "${lastChallenge?.challengeFee} to ${(it?:lastChallenge)?.challengeFee}", )
        lastChallenge = it?:lastChallenge
        it
    }

    val challengeActive = activeChallenge.map {
        it?: return@map false
        it.status!= PodiumChallenge.ChallengeStatus.ENDED
    }.distinctUntilChanged()

    val challengeRunning = activeChallenge.map {
        it?: return@map false
        !it.gameOver
    }.distinctUntilChanged()

    val challengeIAmPartOf = activeChallenge.map { ch ->
        ch?: return@map null
        val iAmParticipant = ch.participantScores.orEmpty().any { it.userId.isSelf() }
        return@map if(!ch.gameOver && iAmParticipant) ch.challengeType else null
    }

    val iAmPartOfRunningChallenge = challengeIAmPartOf.map {
        it!=null
    }

    val showLikeAction = activeChallenge.map {
        it?.challengeType !in listOf(ChallengeType.CONFOUR, ChallengeType.LIKES, ChallengeType.BOXES)
    }

    private fun postChallenge(ch: PodiumChallenge?) {
        if (ch == null || ch.status == PodiumChallenge.ChallengeStatus.ENDED) {
            _activeChallenge.postValue(null)
            _flagChallengeQuestions.postValue(listOf())
            return
        }
        val cur = _activeChallenge.value
        val scores = ch._participantScores?: if (cur!=null && cur.challengeId == ch.challengeId) cur._participantScores else listOf()

        viewModelScope.launch {
            Log.d("PCPC4", "postChallenge: ${ch.status}")
            if (ch.status != PodiumChallenge.ChallengeStatus.ENDED) {
                getFlagChallengeQuestions(ch)
            }
            ch.endTimeUTC = ch.endTimeUTC?:cur?.parsedEndTime?.toEpochSecond()
            ch.contributorRequestedTime = ch.contributorRequestedTime?:cur?.contributorRequestedTime

            val new = ch.copy(_participantScores = scores)
            _podium.value?.challenge = ch
            _activeChallenge.postValue(new)
        }

        Log.d("PCPC4", "postChallenge: $scores | $ch")
    }

    val onChallengeGameOver = LiveEvent<List<PodiumChallengeScore>>()

    val onChallengeParticipantExit = LiveEvent<Int>()

    private val _flagChallengeQuestions: MutableLiveData<List<FlagChallengeQuestion>> = MutableLiveData(listOf())
    val flagChallengeQuestions: LiveData<List<FlagChallengeQuestion>> = _flagChallengeQuestions

    val activeChallengeId = activeChallenge.map {
        it?: return@map null
        return@map Pair(it.challengeId,it.challengeType)
    }.distinctUntilChanged()

    val activeChallengeStatus = activeChallenge.map {
        it?: return@map null
        Log.d("PCP", "activeChallengeStatus: ${it.status}")
        return@map it.status
    }.distinctUntilChanged()

    val onChallengeFacilitatorRequest = LiveEvent<Pair<ChallengeType,Long>>()

    val onChallengeSetupEvent = LiveEvent<PodiumChallengeSetupEvent>()
    val onChallengeScoreUpdate = LiveEvent<ChallengeScoreUpdatePayload>()

    val onFacilitatorRequestResponded = LiveEvent<Boolean>()
    fun respondToFacilitatorRequest(accept: Boolean) {
        val challenge = activeChallenge.value?: return
        val facilitator = challenge.facilitator?: return
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.acceptDeclineFacilitator(challenge.podiumId, challenge.challengeId, facilitator.id, accept)) {
                    is ResultOf.Success -> {
                        onFacilitatorRequestResponded.postValue(accept)
                        if(accept) showInMainScreen(facilitator.id)
                    }
                    is ResultOf.APIError -> {  }
                    is ResultOf.Error -> {  }
                }

            } catch (e: Exception) {
                Log.d("PodiumLCVM", "respondToFacilitatorRequest: ${e.message}")
            }
        }
    }

    val onContributorRequestResponded = LiveEvent<Pair<Double, Boolean>>()
    val onContributorRequestRespondError = LiveEvent<String>()
    val onContributorInsufficientBalance = LiveEvent<Boolean>()
    fun respondToContributorRequest(accept: Boolean) {
        val challenge = activeChallenge.value?: return
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.acceptDeclineContributor(challenge.podiumId, challenge.challengeId, user.id, accept)) {
                    is ResultOf.Success -> {
                        val coin = challenge.contributors.find { it.id.isSelf() }?.coinsSpent?: 0.0
                        onContributorRequestResponded.postValue(Pair(coin, accept))
                    }
                    is ResultOf.APIError -> {
                        if (result.code == 400){
                            onContributorInsufficientBalance.postValue(true)
                        }
                    }
                    is ResultOf.Error -> {  }
                }

            } catch (e: Exception) {
                Log.d("PodiumLCVM", "respondToContributorRequest: ${e.message}")
            }
        }
    }

    val onChallengeContributorRequest = LiveEvent<Pair<PodiumChallenge, PodiumChallenge.ChallengeUser>>()
    val onContributorRequestToSpeakersTimedOut = LiveEvent<Boolean>()

    fun updateChallengeScore(receiver: Int): Boolean {
        return challengeEventRepo.updateChallengeScore(activeChallenge.value?: return false, receiver)
    }

    val navigateToChallengeSetup = LiveEvent<Boolean>()
    fun checkIfChallengeActiveAndNavigate() {
        navigateToChallengeSetup.postValue(activeChallenge.value == null)
    }

    val participantsCountNotMet = LiveEvent<IntRange>()
    val onChallengeStarted = LiveEvent<Boolean>()
    fun startPodiumChallenge() {
        val id = _podiumId.value?: return
        val challenge = activeChallenge.value?: return
        val challengeId = challenge.challengeId
        val challengeType = challenge.challengeType
        var acceptedContributors = challenge.contributors.filter { it.requestAccepted == true }

        if (challenge.contributorType == ChallengeContributionType.SPEAKERS) {
            if (challengeType == ChallengeType.LIKES) {
                acceptedContributors = acceptedContributors.filter { it.id != activeChallenge.value?.facilitator?.id }.toMutableList()
            }

            val range = PodiumChallenge.minParticipants(challengeType)..PodiumChallenge.maxParticipants(challengeType)
            if (acceptedContributors.count() !in range) {
                participantsCountNotMet.postValue(range)
                return
            }
        }

        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.startPodiumChallenge(id, challengeId)) {
                    is ResultOf.Success -> {
                        onChallengeStarted.postValue(true)
                    }
                    is ResultOf.APIError -> {

                    }
                    is ResultOf.Error -> {

                    }
                }
            } catch (e: Exception) {
                Log.d("PodiumLVM", "startPodiumChallenge: ${e.message}")
            }
        }
    }


    val onChallengeClosed = LiveEvent<String>()

    fun closePodiumChallenge() { //Call start podium when the contributor is FREE or YOU
        val id = _podiumId.value?: return
        val challengeId = activeChallenge.value?.challengeId?: return
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result=podiumRepository.closePodiumChallenge(id, challengeId)) {
                    is ResultOf.Success -> {
                        onChallengeClosed.postValue(challengeId)
                        postChallenge(null)
                    }
                    is ResultOf.APIError -> {

                    }
                    is ResultOf.Error -> {
                    }
                }
            } catch (e: Exception) {
                Log.d("PodiumManagerLCVM", "closePodiumChallenge: ${e.message}")
            }
        }
    }

    val deletingChallenge = MutableLiveData(false)
    val challengeDeleted = LiveEvent<Boolean>()
    fun deleteChallenge() {
            val podiumId = podiumId.value?: return
            val challengeId = activeChallenge.value?.challengeId?: return
            deletingChallenge.postValue(true)
            viewModelScope.launch (Dispatchers.IO) {
                try {
                    when (val result = podiumRepository.deletePodiumChallenge(podiumId, challengeId)) {
                        is ResultOf.Success -> {
                            deletingChallenge.postValue(false)
                            challengeDeleted.postValue(true)
                            postChallenge(null)
                        }
                        is ResultOf.APIError, is ResultOf.Error -> {
                            deletingChallenge.postValue(false)
                            challengeDeleted.postValue(false)
                        }
                    }
                } catch (e: Exception) {
                    Log.d("PodiumLCVM", "deleteChallenge: ${e.message}")
                }
            }
    }
    data class ChallengeParticipant(
        val speaker: PodiumSpeaker?,
        val score: PodiumChallengeScore,
    )

    val challengeParticipants = _activeChallenge.map { challenge ->
        challenge?:  return@map null
        return@map challenge.participantScores.map { score->
            ChallengeParticipant(
                speaker = getSpeaker(score.userId), score = score
            )
        }.sortedBy { it.speaker?.name }
    }

    val onInviteToSpeakRequested = LiveEvent<Boolean>()
    val onInviteToSpeakError = LiveEvent<String>()

    fun inviteToSpeak(userId: Int, joinType: TheaterJoinType? = null, invitedForFree: Boolean = true) {
        val id = _podiumId.value?: return
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.inviteToSpeak(id, userId, joinType, invitedForFree)) {
                    is ResultOf.Success -> {
                        val invites = speakerInvites.toMutableList()
                        invites.add(Podium.SpeakerInvite.create(userId,user.id))
                        speakerInvites = invites.toList()
                        Log.d("PLVM-IS", "inviteToSpeak: $speakerInvites")
                        onInviteToSpeakRequested.postValue(true)
                    }
                    is ResultOf.APIError -> {
                        onInviteToSpeakError.postValue(result.error.message)
                    }
                    is ResultOf.Error -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLCVM", "inviteChallengeParticipant: ${e.message}")
            }
        }
    }

    val onInviteToSpeakCancelled = LiveEvent<Boolean>()
    val onInviteToSpeakCancelError = LiveEvent<String>()

    fun cancelInviteToSpeak(userId: Int) {
        val id = _podiumId.value?: return
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.cancelInviteToSpeak(id, userId)) {
                    is ResultOf.Success -> {
                        val invites = speakerInvites.orEmpty().toMutableList()
                        invites.removeIf { it.inviteeId == userId }
                        speakerInvites = invites.toList()
                        Log.d("PLVM-IS", "cancelInviteToSpeak: invites | $speakerInvites")
                        onInviteToSpeakCancelled.postValue(true)
                    }
                    is ResultOf.APIError -> {
                        onInviteToSpeakCancelError.postValue(result.error.message)
                    }
                    is ResultOf.Error -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLCVM", "inviteChallengeParticipant: ${e.message}")
            }
        }
    }


    val onSpeakerInviteReplyError = LiveEvent<String>()
    val onInviteInsufficientBalanceError = LiveEvent<Int>()

    fun replySpeakerInvite(accept : Boolean, joinType: TheaterJoinType?, invitedForFree: Boolean? = true) {
        val pod = _podium.value?: return
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.replySpeakerInvite(pod.id, accept, joinType, invitedForFree)) {
                    is ResultOf.Success -> {
                    }
                    is ResultOf.APIError -> {
                        if (result.code == 402) {
                            val fee = when(joinType) {
                                TheaterJoinType.STAGE -> pod.stageFee?:0
                                TheaterJoinType.AUDIENCE -> pod.audienceFee?:0
                                else -> 0
                            }
                            if (pod.kind == PodiumKind.THEATER) {
                                onTheaterSpeakInsufficientBalance.postValue(fee)
                            } else {
                                pod.audienceFee?.let { onInviteInsufficientBalanceError.postValue(it) }
                            }
                        } else {
                            onSpeakerInviteReplyError.postValue(result.error.message)
                        }
                    }
                    is ResultOf.Error -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLCVM", "replyChallengeInvite: ${e.message}")
            }
        }
    }

    fun replyChallengeInvite(accept : Boolean) {
        val id = _podiumId.value?: return
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.replyChallengeInvite(id, accept)) {
                    is ResultOf.Success -> {
                    }
                    is ResultOf.APIError -> {
                        onSpeakerInviteReplyError.postValue(result.error.message)
                    }
                    is ResultOf.Error -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLCVM", "replyChallengeInvite: ${e.message}")
            }
        }
    }

    private suspend fun getFlagChallengeQuestions(challenge: PodiumChallenge): List<FlagChallengeQuestion>? {
        if (challenge.challengeType != ChallengeType.FLAGS) return null
        _flagChallengeQuestions.value?.let {
            if (it.isNotEmpty()) return it
        }
        when (val result = podiumRepository.getChallengeQuestions(challenge.challengeId, challenge.podiumId)) {
            is ResultOf.Success -> {
                _flagChallengeQuestions.postValue(result.value.questions)
                return result.value.questions
            }
            else -> {}
        }
        return emptyList()
    }

    fun setAnswer(countryCode: String?, answerId: Int?) {
        viewModelScope.launch (Dispatchers.IO) {
            try {
                when (val result =podiumRepository.setAnswer(activeChallenge.value?.podiumId?:"",activeChallenge.value?.challengeId?:"",countryCode, answerId)) {
                    is ResultOf.Success -> {

                    }
                    else -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLCVM", "deleteChallenge: ${e.message}")
            }
        }
    }

    fun canMuteUnmuteSpeaker(speaker: PodiumSpeaker): Boolean {
        val isSelf = speaker.id.isSelf()
        val iAmManager = iAmManager.value==true
        return if(speaker.muted) {
            // Can Unmute?
            if(isSelf) {
                if(isAssemblyPodium) isCurrentAssemblySpeaker.value == true || iAmManager
                else if (isLecturePodium) activeChallenge.value?.challengeType !in setOf(ChallengeType.FLAGS,ChallengeType.PENALTY,ChallengeType.CONFOUR)
                else true
            }
            //Manager can unmute an audience speaker in theater
            else isTheaterPodium && isSpeakerInAudience(speaker) && iAmManager
        } else {
            // Can mute?
            if (isSelf) true
            else if (isAssemblyPodium) false
            else iAmElevated.value==true && !isManager(speaker.id)
        }
    }

    val showMuteToggle: LiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(true)
        fun update() {
            val show = if (iAmSpeaker.value!=true) false
            else if (isAssemblyPodium) isCurrentAssemblySpeaker.value == true || iAmManager.value == true
            else if (isLecturePodium) activeChallenge.value?.challengeType !in setOf(ChallengeType.FLAGS,ChallengeType.PENALTY,ChallengeType.CONFOUR)
            else true
            med.postValue(show)
        }
        med.addSource(iAmSpeaker) { update() }
        med.addSource(iAmMuted) { update() }
        med.addSource(activeChallenge) { update() }
        med.addSource(podiumKind) { update() }
        med.addSource(iAmManager) { update() }
        med.addSource(isCurrentAssemblySpeaker) { update() }
        med.distinctUntilChanged()
    }

    // Penalty challenge

    val onPenaltyPlayerReady = LiveEvent<Int>()
    val onPenaltyStartTurn = LiveEvent<PenaltyData>()
    val onPenaltySelectTarget = LiveEvent<PodiumChallengePenaltyTargetPayload>()
    val onPenaltyKickResult = LiveEvent<PodiumChallengePenaltyKickResultPayload>()

    fun setPenaltyPlayerReady(userId: Int) {
        val podiumId = podiumId.value?: return
        val challengeId = activeChallenge.value?.challengeId?: return
        challengeEventRepo.setPenaltyPlayerReady(PodiumChallengePenaltyReadyPayload(podiumId, challengeId, userId))
    }

    fun setPenaltyTarget(userId: Int, kick: PenaltyKickTarget) {
        val podiumId = podiumId.value?: return
        val challengeId = activeChallenge.value?.challengeId?: return
        challengeEventRepo.setPenaltyTarget(PodiumChallengePenaltyTargetPayload(podiumId, challengeId, userId, kick.target))
    }

    //contribution logics

    private val _contributorsList : LiveData<List<PodiumChallenge.ChallengeUser>?> = activeChallenge.map {
        it?: return@map null
        it.contributors
    }

    val contributorsList: MediatorLiveData<List<PodiumChallenge.ChallengeUser>?> by lazy {
        val med = MediatorLiveData<List<PodiumChallenge.ChallengeUser>?>()
        fun update() {
            val data = _contributorsList.value?.map { contributor ->
                contributor.copy(
                    name = nickNames.nickNameOrName(contributor.id, contributor.name)
                )
            }
            med.postValue(data)
        }
        med.addSource(nickNamesLiveData) { update() }
        med.addSource(_contributorsList) { update() }
        med
    }

    val onSingleContributorTimedOut = LiveEvent<Boolean>()

    private fun checkAndShowContributorTimedOut() {
        val challenge = activeChallenge.value?: return
        val contributor = challenge.contributors.firstOrNull()
        if (contributor!= null && challenge.contributorRequestedTimeRemaining.seconds <= 0L && contributor.timeResponded == null ){
            onSingleContributorTimedOut.postValue(true)
        }

    }

    val startTimerForConFourParticipantsSheet = LiveEvent<Long>()

    val onConnectFourChallengeCoinDropped = LiveEvent<ConFourTokenDroppedPayload>()

    fun dropConFourToken(slot: ConnectFourBoard.DropPoint, player: ChallengePlayer, gameStatus: ConFourGameStatus?) {
        Log.w("PCPC4", "dropConFourToken: challenge; ${activeChallenge.value}")

        val myUserId = user.id
        val opponentUserId = _activeChallenge.value?.participantScores?.find { it.userId != myUserId }?.userId ?: return
        Log.w("PCPC4", "dropConFourToken: myUserId; $myUserId opponentUserId $opponentUserId")
        if (challengeEventRepo.connected){
            challengeEventRepo.dropConFourToken(ConFourTokenDropPayload.from(
                challenge = activeChallenge.value?: return,
                slot = slot,
                player = player,
                status = gameStatus,
                myUserId = myUserId,
                opponentUserId = opponentUserId
            )
            )
        }else{
            networkError.postValue(true)
        }
    }

    val networkError = LiveEvent<Boolean>()

    fun sendConFourTimedOut(player: ChallengePlayer, currentPlayerId: Int) {
        Log.w("PCPC4", "sendConFourTimedOut: challenge; ${activeChallenge.value}")
        val opponentUserId = _activeChallenge.value?.participantScores?.find { it.userId != currentPlayerId }?.userId ?: return
        Log.w("PCPC4", "sendConFourTimedOut:  currentPlayerId; $currentPlayerId opponentUserId $opponentUserId\"")

        challengeEventRepo.informConFourTimeout(ConFourDropTimedOutPayload.from(
            challenge = activeChallenge.value?: return,
            player = player,
            currentPlayerId = currentPlayerId,
            opponentUserId = opponentUserId
            )
        )
    }

    fun onExitChallenge() {
        val pId = _podiumId.value ?: return
        val challenge = activeChallenge.value ?: return
        val cId = challenge.challengeId

        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (podiumRepository.exitChallenge(pId, cId)) {
                    is ResultOf.Success -> {
                    }

                    is ResultOf.APIError -> {
                    }

                    is ResultOf.Error -> {
                    }
                }
            } catch (e: Exception) {
                Log.d("PodiumLCVM", "onExitChallenge: ${e.message}")
            }
        }
    }

    val onNavigateToPrivateMessage = LiveEvent<Pair<String, Int>>()

    fun navigateToPrivateMessage(id: Int) {
        viewModelScope.launch {
            val roomId = HuddlesRepository(getApplication()).getPrivateChatRoomId(id)
            onNavigateToPrivateMessage.postValue(Pair(roomId, id))
        }
    }

    val onFollowedUser = LiveEvent<String>()

    val onUnfollowedUser = LiveEvent<String>()

    fun toggleFollow(id : Int, name :String, isFollowed: Boolean) {
        if (isFollowed) unFollowUser(id, name) else followUser(id, name)
    }

    private fun followUser(id : Int, name :String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (profileRepo.followUser(id)) {
                is ResultOf.Success -> {
                    onFollowedUser.postValue(name)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    private fun unFollowUser(id : Int, name : String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (profileRepo.unFollowStar(id)) {
                is ResultOf.Success -> {
                    onUnfollowedUser.postValue(name)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    val challengeParticipantsList: MediatorLiveData<List<PodiumChallenge.ChallengeUser>> by lazy {
        val med = MediatorLiveData<List<PodiumChallenge.ChallengeUser>>()
        fun update() {
            val data = activeChallenge.value?.waitingParticipants.orEmpty().map { contributor ->
                contributor.copy(
                    name = nickNames.nickNameOrName(contributor.id, contributor.name)
                )
            }
            med.postValue(data)
        }
        med.addSource(nickNamesLiveData) { update() }
        med.addSource(activeChallenge) { update() }
        med
    }

    data class CameraPurchaseInfo(
        val remainingDays: Int?=null,
        val requiredFlix: String?=null,
    )
    val cameraPurchaseInfo = MutableLiveData<CameraPurchaseInfo>()

    fun getPodiumBuyCameraDetails(){
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.getPodiumBuyCameraDetails()) {
                    is ResultOf.Success -> {
                        val info = CameraPurchaseInfo(
                            remainingDays = result.value.camera_time?:0,
                            requiredFlix = result.value.price?.toInt().toString(),
                        )
                        cameraPurchaseInfo.postValue(info)
                    }
                    is ResultOf.APIError -> {}
                    is ResultOf.Error -> {}
                }

            } catch (e: Exception) {
                Log.d("PodiumBuyCamera", "buyCameraException: ${e.message}")
            }
        }
    }

     val onCameraPurchaseSuccess = LiveEvent<Boolean>()
    val onCameraPurchaseError = LiveEvent<String>()

    fun buyCameraTime(buyCamera: Boolean) {

        viewModelScope.launch(Dispatchers.IO) {
            try {
             when (val result=podiumRepository.buyCameraTime(buyCamera)) {
                    is ResultOf.Success -> {
                        onCameraPurchaseSuccess.postValue(true)
                    }
                    is ResultOf.APIError -> {
                        onCameraPurchaseError.postValue(result.error.message)
                    }
                    is ResultOf.Error -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLCVM", "buyCameraException: ${e.message}")
            }
        }
    }

    fun hideUnHideLiveUsers(hideValue:Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val request =HideLiveUsersRequest(hide = hideValue)
                when (podiumRepository.postHideLiveUsers(podiumId =  podiumId.value!!, requestBody = request)) {
                    is ResultOf.Success -> {
                    }
                    else -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLVM", "requestToSpeak: error: ${e.message}")
            }
        }
    }

    // Interview Podium

    val secondMainScreenSpeaker = _speakers.map {
        Log.w("PLVM", "secondMainScreenSpeaker for: ${podiumKind.value}")
        it.find { sp -> sp.speaker.showOnStage == true }
    }

    val secondMainScreenSpeakerId = secondMainScreenSpeaker.map {
        it?.speaker?.id
    }.distinctUntilChanged()

    fun isSecondMainScreenSpeaker(speaker: PodiumSpeaker): Boolean {
        return secondMainScreenSpeaker.value?.speaker?.id == speaker.id
    }

    val showSecondVideoSurface: LiveData<Boolean> by lazy {
        val med = MediatorLiveData(true)
        fun update() {
            val msId = secondMainScreenSpeakerId.value
            val state = remoteVideoState.value.orEmpty().getOrDefault(msId, null)
            val show = if (msId.isSelf()) _myVideoIsTurnedOn.value==true
            else state == AgoraEngineService.VideoState.PLAYING
            med.postValue(show)
        }
        med.addSource(secondMainScreenSpeakerId) { update() }
        med.addSource(_myVideoIsTurnedOn) { update() }
        med.addSource(remoteVideoState) { update() }
        med.distinctUntilChanged()
    }

    val allowScrollToAdjacentPodium: LiveData<Boolean> by lazy {
        val med = MediatorLiveData(true)
        fun update() {
            //as per new requirement, user can swipe even though he is speaking
//            med.postValue(iAmSpeaker.value != true && iAmManager.value != true)
            med.postValue(iAmManager.value != true)
        }
        med.addSource(iAmSpeaker) { update() }
        med.addSource(iAmManager) { update() }
        med.distinctUntilChanged()

    }
    
    private fun deleteChatById(chatId: String) {
        val currentList = _liveChat.value?.toMutableList() ?: return

        val indexToRemove = currentList.indexOfFirst { it.chatId == chatId }

        if (indexToRemove != -1) {
            currentList.removeAt(indexToRemove)
            _liveChat.postValue(currentList)
        }
    }

    // Maidan

    sealed class MaidanStatusData(open val statusEnum: Podium.MaidanStatus) {
        data object Waiting: MaidanStatusData(Podium.MaidanStatus.WAITING)
        data class ChallengeActive(val endTime: ZonedDateTime, override val statusEnum: Podium.MaidanStatus): MaidanStatusData(statusEnum)
        data class TalkTime(val endTime: ZonedDateTime): MaidanStatusData(Podium.MaidanStatus.TALKTIME)

        val isLive: Boolean
            get() = this is ChallengeActive
    }

    private val _maidanStatus = podium.map {
        it?.maidanStatus
    }

    enum class MaidanCompetitorStatus {
        NONE,
        WAITING,
        CHALLENGE_NEW,
        INVITE
    }

    private val onRecheckCompetitorStatus = LiveEvent<Unit>()

    fun recheckCompetitorStatus() {
        Log.w("PLVM", "maidanCompetitorStatus: recheckCompetitorStatus")
        onRecheckCompetitorStatus.postValue(Unit)
    }

    val maidanCompetitorStatus: LiveData<MaidanCompetitorStatus> by lazy {
        val med = MediatorLiveData(MaidanCompetitorStatus.NONE)
        fun update() {
            val challenge = _activeChallenge.value
            val status = if(secondMainScreenSpeakerId.value!=null) MaidanCompetitorStatus.NONE
                else if (iAmManager.value==true && (challenge==null || challenge.gameOver)) MaidanCompetitorStatus.CHALLENGE_NEW
            else if(iAmManager.value==true && challenge?.hasContributorRequestTimedOut==true) MaidanCompetitorStatus.INVITE
            else MaidanCompetitorStatus.WAITING
            Log.w("PLVM", "maidanCompetitorStatus: $status | ${challenge?.contributorRequestedTimeRemaining?.seconds} sec remaining")
            med.postValue(status)
        }
        med.addSource(_activeChallenge) { update() }
        med.addSource(secondMainScreenSpeakerId) { update() }
        med.addSource(onRecheckCompetitorStatus) { update() }
        med.distinctUntilChanged()
    }

    val maidanStatusData: LiveData<MaidanStatusData> by lazy {
        val med = MediatorLiveData<MaidanStatusData>(MaidanStatusData.Waiting)
        fun update() {
            when(val status = _maidanStatus.value) {
                Podium.MaidanStatus.CHALLENGE_LIVE, Podium.MaidanStatus.EXTRA_TIME -> {
                    Log.w("PLVM", "observe: status is $status ending at ${activeChallenge.value?.parsedEndTime}")
                    med.postValue(MaidanStatusData.ChallengeActive(activeChallenge.value?.parsedEndTime?:ZonedDateTime.now(),status))
                }
                Podium.MaidanStatus.TALKTIME -> {
                    Log.w("PLVM", "observe: status is $status ending at ${podium.value?.talkTimeEnd}")
                    med.postValue(MaidanStatusData.TalkTime(podium.value?.talkTimeEnd?: ZonedDateTime.now()))
                }
                Podium.MaidanStatus.WAITING -> med.postValue(MaidanStatusData.Waiting)
                null -> med.postValue(MaidanStatusData.Waiting)
            }
        }
        med.addSource(_maidanStatus) { update() }
        med.addSource(_activeChallenge) { update() }
        med.distinctUntilChanged()
    }

    val canChallengeAgain: LiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            med.postValue(meAsSpeaker.value!=null && maidanStatusData.value is MaidanStatusData.TalkTime && secondMainScreenSpeakerId.value!=null)
        }
        med.addSource(meAsSpeaker) { update() }
        med.addSource(maidanStatusData) { update() }
        med.addSource(secondMainScreenSpeakerId) { update() }
        med.distinctUntilChanged()
    }

    private val _maidanLiveLikes = MutableLiveData<MutableList<PodiumMaidanLikePayload>>(mutableListOf())
    val maidanLiveLikes: LiveData<List<PodiumMaidanLikePayload>> = _maidanLiveLikes.map {
        it.map { plc ->
            plc.user.name = nickNames.nickNameOrName(plc.user)
            plc
        }
    }

    private fun MutableLiveData<MutableList<PodiumMaidanLikePayload>>.addAndPost(item: PodiumMaidanLikePayload) {
        var list = this.value?.apply {
            add(0, item)
        }.orEmpty().take(500)
        this.postValue(list.toMutableList())
    }

    val likesNeeded: LiveData<Int?> by lazy {
        val med = MediatorLiveData<Int?>(null)
        fun update() {
            fun getNeed(acs: PodiumChallenge?): Int? {
                acs?: return null
                val manager = _podium.value?.managerId?: return null
                return if(acs.running) {
                    val opponentScores = acs.participantScores.find { it.id != manager }?.scoreInt?:return null
                    val myScores = acs.participantScores.find { it.id == manager }?.scoreInt?:return null
                    if (myScores<=opponentScores) opponentScores-myScores+1
                    else null
                } else null
            }
            med.postValue(if(_maidanStatus.value.isChallengeTime()) getNeed(_activeChallenge.value) else null)
        }
        med.addSource(_activeChallenge) { update() }
        med.addSource(_maidanStatus) { update() }
        med.distinctUntilChanged()
    }

    val iAmMaidanCompetitor: LiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(null)
        fun update() {
            med.postValue(meAsSpeaker.value!=null || _podium.value?.managerId.isSelf() || _podium.value?.competitorUserId.isSelf())
        }
        med.addSource(_podium) { update() }
        med.addSource(meAsSpeaker) { update() }
        med.distinctUntilChanged()
    }

    data class MaidanProgress(
        val firstPoints: Int = 0,
        val secondPoints: Int = 0,
        val progress: Int = 50
    )

    val maidanProgress = _activeChallenge.map { ac ->
        ac?: return@map null
        val firstP = ac.participantScores.find { it.userId == mainScreenSpeakerId.value }?.scoreInt?:0
        val secondP = ac.participantScores.find { it.userId == secondMainScreenSpeakerId.value }?.scoreInt?:0
        val progress = if (firstP+secondP==0) 0.5f
        else firstP.toFloat()/(firstP+secondP)
        return@map MaidanProgress(firstP,secondP,(progress*100).roundToInt())
    }

    private val _maidanLikeSending = MutableLiveData(false)
    val maidanLikeSending: LiveData<Boolean> = _maidanLikeSending

    val onMaidanLikeError = LiveEvent<String>()

    fun sendMaidanLike(like: MaidanLike) {
        _maidanLikeSending.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val pId = podiumId.value?: return@launch
                val challenge = activeChallenge.value?:return@launch
                when (val result = podiumRepository.sendMaidanLike(podiumId = pId, challengeId = challenge.challengeId, like = like)) {
                    is ResultOf.Success -> {
                        _activeChallenge.value?.let { acs ->
                            acs.participantScores.find { it.userId == mainScreenSpeakerId.value }?.let {
                                it.score = max(it.score,result.value.likes.toDouble())
                            }
                            _activeChallenge.postValue(acs)
                        }
                        _maidanCoinBalance.postValue(Pair(result.value.coinsGiven?:0, result.value.coinBalance?:0.0))
                    }
                    is ResultOf.APIError -> {
                        onMaidanLikeError.postValue(result.errorMessage().orEmpty())
                    }
                    else -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLVM", "requestToSpeak: error: ${e.message}")
            }
            _maidanLikeSending.postValue(false)
        }
    }

    private val _followManagerLoading = MutableLiveData<Boolean?>(null)
    val followManagerLoading: LiveData<Boolean?> = _followManagerLoading

    private fun checkFollowStatus(podium: Podium) {
        if (podium.kind!=PodiumKind.MAIDAN) return
        if (podium.managerId.isSelf()) return
        _followManagerLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            updateFollowStatus(podium)
            _followManagerLoading.postValue(false)
        }
    }

    private suspend fun updateFollowStatus(podium: Podium): Boolean? {
        if (podium.managerId==user.id) return null
        when (val result = podiumRepository.getPodiumUserLikesCoins(podium.id, podium.managerId)) {
            is ResultOf.Success -> {
                Log.d("PodiumLVM", "updateFollowStatus: ${result.value}")
                _starType.postValue(
                    if (result.value.isSuperstar) StarType.SUPERSTAR
                    else if (result.value.isFollowed) StarType.STAR
                    else null
                )
                _relation.postValue(result.value.relation)
                return result.value.isFollowed
            }
            else -> {}
        }
        return null
    }

    fun confirmFollowing(onFollowed: (Boolean) -> Unit) {
        viewModelScope.launch(Dispatchers.IO) {
            val following = _starType.value != null
            viewModelScope.launch(Dispatchers.Main) {
                onFollowed.invoke(following)
            }
        }
    }

    private val _starType = MutableLiveData<StarType?>(null)
    val starType: LiveData<StarType?> = _starType.distinctUntilChanged()

    private val _relation = MutableLiveData<FollowerType?>(null)
    val relation: LiveData<FollowerType?> = _relation


    fun followManager() {
        if (_starType.value!=null) return
        val managerId = podium.value?.managerId?: return
        _followManagerLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (profileRepo.followUser(managerId)) {
                is ResultOf.Success -> {
                    _starType.postValue(StarType.STAR)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
        _followManagerLoading.postValue(false)
    }

    private val _maidanCoinBalance = MutableLiveData(Pair<Int,Double>(0,0.0))
    val maidanCoinBalance: LiveData<Pair<Int,Double>> = _maidanCoinBalance

    fun exitMaidan() {
        val pod = _podiumId.value ?: return
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.endMaidanChallenge(pod)) {
                    is ResultOf.Success -> {
                        agoraSession?.leave()
                        onExitPodium.postValue(true)
                    }
                    is ResultOf.APIError -> {}
                    is ResultOf.Error -> {}
                }

            } catch (e: Exception) {
                Log.d("PodiumLVM", "exit: error: ${e.message}")
            }
        }
    }

    val onNewMaidanCreated = LiveEvent<PodiumChallenge>()
    val challengeError = LiveEvent<String?>()
    fun challengeAgain(challengeNew: Boolean = false, prize: Int? = null) {
        Log.d("PodiumLVM", "challengeAgain ${_podium.value}")
        Log.d("PodiumLVM", "challengeAgain ${_podium.value?.challenge}")
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = if (challengeNew) {
                    podiumRepository.maidanChallengeAgain(podiumId = podiumId.value ?: return@launch, prize = prize)
                } else {
                    podiumRepository.maidanChallengeAgain(challengeId = lastChallenge?.challengeId ?: return@launch)
                }) {
                    is ResultOf.Success -> {
                        if (challengeNew) onNewMaidanCreated.postValue(result.value) else postChallenge(result.value)
                    }

                    is ResultOf.APIError -> {
                        challengeError.postValue(result.error.message)
                    }
                    is ResultOf.Error -> {
                        challengeError.postValue(result.exception.message)
                    }
                }

            } catch (e: Exception) {
                Log.d("PodiumLVM", "exit: error: ${e.message}")
            }
        }
    }

    fun respondToChallengeAgain(podiumId: String, challengeId: String, accept: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            if (accept) {
                when (val result = podiumRepository.acceptMaidanCompetitor(podiumId, challengeId)) {
                    is PodiumRepository.JoinResultOf.Success -> {
                        val coin = result.value?.challenge?.competitorFee ?: 0.0
                        result.value?.let {
                            onContributorRequestResponded.postValue(Pair(coin, true))
                        }
                        onContributorRequestResponded.postValue(Pair(coin,true))
                    }
                    is PodiumRepository.JoinResultOf.APIError -> {
                        if (result.error.result?.reason == PodiumJoinErrorResponse.PodiumJoinErrorReason.INSUFFICIENT_BALANCE) {
                            onContributorInsufficientBalance.postValue(true)
                        } else onContributorRequestRespondError.postValue(result.error.message)
                    }
                    is PodiumRepository.JoinResultOf.Error -> {}
                }
            } else {
                when (val result = podiumRepository.declineMaidanCompetitor(podiumId, challengeId)) {
                    is ResultOf.Success -> onContributorRequestResponded.postValue(Pair(0.0, false))
                    is ResultOf.APIError -> onContributorRequestRespondError.postValue(result.error.message)
                    is ResultOf.Error -> {}
                }
            }
        }
    }

    private fun isSpeakerInAudience(speaker: PodiumSpeaker): Boolean {
        return isSpeaker(speaker.id) && !isMainScreenSpeaker(speaker) && !isSecondMainScreenSpeaker(speaker)
    }

    val onTheaterSpeakInsufficientBalance = LiveEvent<Int>()
    val onTheaterSpeakError = LiveEvent<String>()

    fun requestToSpeak(type: TheaterJoinType) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val pod = _podium.value?: return@launch
                when (val result = podiumRepository.requestToSpeak(podiumId.value?: return@launch, type)) {
                    is ResultOf.Success -> {
                        onRequestedToSpeak.postValue(true)
                    }
                    is ResultOf.APIError -> {
                        if (result.code == 402) {
                            val fee = when(type) {
                                TheaterJoinType.MAIN_SCREEN -> 0
                                TheaterJoinType.STAGE -> pod.stageFee?:0
                                TheaterJoinType.AUDIENCE -> pod.audienceFee?:0
                            }
                            onTheaterSpeakInsufficientBalance.postValue(fee)
                        } else {
                            onTheaterSpeakError.postValue(result.error.message)
                        }
                    }
                    else -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLVM", "requestToSpeak: error: ${e.message}")
            }
        }
    }

    val theaterLastMessage: LiveData<PodiumLiveChat> by lazy {
        val med = MediatorLiveData<PodiumLiveChat>()
        fun update() {
            var latest = liveChat.value?.firstOrNull()?:createDummyChatMessage(_podium.value?.bio.orEmpty())
            med.postValue(latest)
        }
        med.addSource(_podium) { update() }
        med.addSource(liveChat) { update() }
        med.distinctUntilChanged()
    }

    val showTheaterChatInput = MutableLiveData(false)

    fun toggleTheaterChatInput(show: Boolean) {
        if (show) {
            clearChatText()
            showTheaterChatInput.postValue(true)
        } else {
            showTheaterChatInput.postValue(false)
        }
    }

    fun setUpPaidLike(checked: Boolean) {
        if(checked) skipNextTime.postValue(true) else skipNextTime.postValue(false)
    }

    fun checkIfPodiumLive(podiumId: String, onLive: (Boolean) -> Unit) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                Log.d("PodiumLVM", "trying to leave")
                when (val result = podiumRepository.getPodiumDetails(podiumId)) {
                    is ResultOf.Success -> {
                        Log.d("PodiumLVM", "leave success")
                        withContext(Dispatchers.Main) {
                            onLive.invoke(result.value.isLive)
                        }
                    }

                    is ResultOf.APIError -> {}

                    is ResultOf.Error -> {}
                }

            } catch (e: Exception) {
                Log.d("PodiumLVM", "leave: error: ${e.message}")
            }
        }
    }

    val yallaGuysCount = _podium.map {
        return@map it?.yallaChallengesCount?.toString().orEmpty()
    }

    fun pauseResumeYallaGuys(pause:Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val pod = _podiumId.value?: return@launch
                val res = if (pause) podiumRepository.pauseYallaGuys(pod) else podiumRepository.resumeYallaGuys(pod)
                when (res) {
                    is ResultOf.Success -> {
                    }
                    else -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLVM", "requestToSpeak: error: ${e.message}")
            }
        }
    }

    val onYallaCompetitorRequest = LiveEvent<YallaCompetitorRequestPayload>()
    val onYallaNotification = LiveEvent<String>()

    val onBoxChallengeLineDrawn = LiveEvent<BoxChallengeLineDrawnPayload>()

    fun drawBoxChallengeLine(line: BoxChallengeBoardModel.Line, player: ChallengePlayer, gameStatus: ConFourGameStatus?) {
        Log.w("PCPC4", "dropConFourToken: challenge; ${activeChallenge.value}")

        val myUserId = user.id
        val opponentUserId = _activeChallenge.value?.participantScores?.find { it.userId != myUserId }?.userId ?: return
        Log.w("PCPC4", "dropConFourToken: myUserId; $myUserId opponentUserId $opponentUserId")
        if (challengeEventRepo.connected){
            challengeEventRepo.drawBoxLine(BoxChallengeLineDrawPayload.from(
                challenge = activeChallenge.value?: return,
                line = line,
                player = player,
                status = gameStatus,
                myUserId = myUserId,
                opponentUserId = opponentUserId
            )
            )
        }else{
            networkError.postValue(true)
        }
    }

    fun sendBoxChallengeTimedOut(player: ChallengePlayer, currentPlayerId: Int) {
        Log.w("PCPC4", "sendConFourTimedOut: challenge; ${activeChallenge.value}")
        val opponentUserId = _activeChallenge.value?.participantScores?.find { it.userId != currentPlayerId }?.userId ?: return
        Log.w("PCPC4", "sendConFourTimedOut:  currentPlayerId; $currentPlayerId opponentUserId $opponentUserId\"")

        challengeEventRepo.informBoxLineTimeout(ConFourDropTimedOutPayload.from(
            challenge = activeChallenge.value?: return,
            player = player,
            currentPlayerId = currentPlayerId,
            opponentUserId = opponentUserId
        )
        )
    }

    fun submitKnowledgeRaceAnswer(question: KnowledgeRaceData.Question, opt: KnowledgeRaceData.Option) {
        Log.w("PCPKR", "submitKnowledgeRaceAnswer: $opt")
        val myUserId = user.id
        val challenge = _activeChallenge.value ?: return
//        val opponentUserId = _activeChallenge.value?.participantScores?.find { it.userId != myUserId }?.userId ?: return
        if (challengeEventRepo.connected) {
            challengeEventRepo.answerKnowledgeRaceQuestion(
                KnowledgeRaceAnswerPayload(
                    challenge.challengeId, challenge.podiumId, question.id, opt.id, myUserId
                )
            )
        } else {
            networkError.postValue(true)
        }
    }

    val canCommentByUserRating: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            val podiumDetail = _podium.value
            val userRating = _accountDetails.value?.flaxRatePercentage ?: 0
            val canComment = userRating >= (podiumDetail?.requiredRatingToComment ?: 0) || podiumDetail?.isAdmin == true || podiumDetail?.isManager == true
            med.postValue(canComment)
        }
        med.addSource(_podium) { update() }
        med.addSource(_accountDetails) { update() }
        med
    }

    val canSpeakByUserRating: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            val podiumDetail = _podium.value
            val userRating = _accountDetails.value?.flaxRatePercentage ?: 0
            val canSpeak = userRating >= (podiumDetail?.requiredRatingToSpeak ?: 0) || podiumDetail?.isAdmin == true || podiumDetail?.isManager == true
            Log.d("PLVM", "Speak update() $canSpeak")
            med.postValue(canSpeak)
        }
        med.addSource(_podium) { update() }
        med.addSource(_accountDetails) { update() }
        med
    }

    val isAdvancedMenuVisible = _podium.map {
        when (it?.kind) {
            PodiumKind.THEATER, PodiumKind.ASSEMBLY, PodiumKind.LECTURE -> it.isManager == true
            else -> false
        }
    }

    private val _knowledgeRaceSettings = MutableLiveData(
        PodiumKnowledgeRaceChallengePresenter.KnowledgeRaceSettings(
            language = LocaleUtil.getAppLocale(), music = false, soundEffects = false
        )
    )

    val knowledgeRaceSettings: LiveData<PodiumKnowledgeRaceChallengePresenter.KnowledgeRaceSettings> = _knowledgeRaceSettings

    fun updateSettings(
        language: AppLocale? = null,
        music: Boolean? = null,
        soundEffects: Boolean? = null,
    ) {
        val current = _knowledgeRaceSettings.value ?: return
        _knowledgeRaceSettings.value = current.copy(
            language = language ?: current.language, music = music ?: current.music, soundEffects = soundEffects ?: current.soundEffects
        )
    }

    val onKnowledgeRaceElimination = LiveEvent<List<PodiumChallengeScore>>()
}
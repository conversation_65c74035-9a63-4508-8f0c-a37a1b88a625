package com.app.messej.ui.home.publictab.podiums.live

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.appcompat.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.databinding.FragmentPodiumLiveInterviewBinding
import com.app.messej.databinding.ItemPodiumLikesContainerBinding
import com.app.messej.databinding.ItemPodiumLiveChatAboutBinding
import com.app.messej.databinding.ItemPodiumSpeakerMainBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.databinding.LayoutPodiumChatPausedEmptyBinding
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPodiumPromoBoard
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.CHALLENGE_RUNNING
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.DISABLED_BY_ADMIN
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.DISABLED_BY_MANAGER
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.UNKNOWN
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showPodiumCoinInfoDialog
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showPodiumLikeInfoDialog
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.button.MaterialButton
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kennyc.view.MultiStateView
import com.scwang.smart.refresh.layout.SmartRefreshLayout


class PodiumLiveInterviewFragment : PodiumLiveAbstractFragment() {
    override val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

    val args: PodiumLiveInterviewFragmentArgs by navArgs()

    override val podiumIdArg: String
        get() = args.podiumId

    override val enableScrollForTab: PodiumTab?
        get() = PodiumTab.fromInt(args.enableScrollForTab)

    override lateinit var binding: FragmentPodiumLiveInterviewBinding

    override val liveBindingElements = object: LiveBindingElements {

        override val liveChat: RecyclerView
            get() = binding.liveChat

        override fun showLocalVideoSurface(show: Boolean) {
            binding.mainScreen.showVideo = show
        }

        override fun onPodiumLoading(loading: Boolean) {
            binding.header.podiumHeaderMultiStateView.viewState = if (loading) MultiStateView.ViewState.LOADING else MultiStateView.ViewState.CONTENT
        }

        override val likesContainer: ItemPodiumLikesContainerBinding
            get() = binding.likesContainer

        override val chatSend: MaterialButton
            get() = binding.chatSendButton

        override val actionPaidLike: MaterialButton
            get() = binding.actionLike

        override val actionShare: MaterialButton
            get() = binding.actionShare

        override val actionDecorHolderTop: LinearLayoutCompat
            get() = binding.actionDecorHolderTop

        override val liveCounter: ViewGroup
            get() = binding.header.liveCounter

        override val scrollerLayout: SmartRefreshLayout
            get() = binding.refreshLayout

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_live_interview, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    private lateinit var liveChatAbout: ItemPodiumLiveChatAboutBinding

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        liveChatAbout = DataBindingUtil.inflate<ItemPodiumLiveChatAboutBinding?>(layoutInflater, R.layout.item_podium_live_chat_about, null, false).apply {
            mLiveChatAdapter?.addFooterView(this.root)
        }
        setup()
        addObservers()
    }

    override fun ItemPodiumSpeakerMainBinding.onMainScreenSetup() {
        showCameraToggle = true
    }

    private fun setup() {
        setEmptyView()
        binding.header.podiumDp.setOnClickListener {
            showMoreMenu(it)
        }
//        binding.header.exitButton.setOnClickListener {
//            onExit()
//        }
        binding.mainScreen.clickTarget.setOnClickListener {
            val item = viewModel.mainScreenSpeaker.value ?: return@setOnClickListener
            onSpeakerItemClick(item)
        }
        binding.mainScreenTwo.clickTarget.setOnClickListener {
            val item = viewModel.secondMainScreenSpeaker.value ?: return@setOnClickListener
            onSpeakerItemClick(item)
        }

        binding.mainScreenTwo.placeholderPlusButton.setOnClickListener {
            if (viewModel.iAmManager.value == true) {
                val action = PodiumLiveInterviewFragmentDirections.actionPodiumLiveInterviewFragmentToPodiumLiveUsersListBottomSheetFragment()
                findNavController().navigateSafe(action)
            }
        }

        binding.mainScreenTwo.mainScreen.setOnClickListener {
            val id = viewModel.secondMainScreenSpeakerId.value?: return@setOnClickListener
            val action = PodiumLiveInterviewFragmentDirections.actionPodiumLiveInterviewFragmentToPodiumSpeakerActionsBottomSheetFragment(id)
            findNavController().navigateSafe(action)
        }

        setupPodiumPromoBoard(binding.ticker)

        binding.header.likeCounter.setOnClickListener {
            showPodiumLikeInfoDialog()
        }

        binding.header.coinCounter.setOnClickListener {
            showPodiumCoinInfoDialog()
        }
    }

    private fun onSpeakerItemClick(item: ActiveSpeakerUIModel) {
        val action = PodiumLiveInterviewFragmentDirections.actionPodiumLiveInterviewFragmentToPodiumSpeakerActionsBottomSheetFragment(item.speaker.id)
        findNavController().navigateSafe(action)
    }

    private fun addObservers() {
        viewModel.podium.observe(viewLifecycleOwner) {
            liveChatAbout.hostName = it?.managerName.orEmpty()
            liveChatAbout.about = it?.bio.orEmpty()
        }
        viewModel.iAmManager.observe(viewLifecycleOwner) {
            binding.mainScreenTwo.placeholderPlusButton.isClickable = it==true
        }

        viewModel.chatDisabled.observe(viewLifecycleOwner) {
            binding.liveChatMultiStateView.viewState = MultiStateView.ViewState.CONTENT

            binding.chatTextBoxMultiStateView.viewState = if (it == null) MultiStateView.ViewState.CONTENT
            else if (viewModel.iAmManager.value == true) MultiStateView.ViewState.CONTENT
            else when (it) {
                CHALLENGE_RUNNING -> MultiStateView.ViewState.CONTENT
                DISABLED_BY_MANAGER -> {
                    chatTextBoxEmptyView.edsEmptyMessage.setText(R.string.podium_comments_disabled_manager)
                    MultiStateView.ViewState.EMPTY
                }

                DISABLED_BY_ADMIN -> {
                    chatTextBoxEmptyView.edsEmptyMessage.setText(R.string.podium_comments_disabled_admin)
                    MultiStateView.ViewState.EMPTY
                }

                UNKNOWN -> {
                    chatTextBoxEmptyView.edsEmptyMessage.setText(R.string.podium_comments_disabled)
                    MultiStateView.ViewState.EMPTY
                }
            }
        }

        viewModel.mainScreenSpeaker.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: mainScreenSpeaker $it")
            setMainScreenSpeaker(binding.mainScreen,it)
        }

        viewModel.secondMainScreenSpeaker.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: mainScreenSpeakerTwo $it")
            setMainScreenSpeaker(binding.mainScreenTwo,it)
        }
        viewModel.mainScreenSpeakerId.observe(viewLifecycleOwner) {
            it?: return@observe
            binding.mainScreen.setupMainScreen(it)
        }
        viewModel.secondMainScreenSpeakerId.observe(viewLifecycleOwner) {
            Log.w("PLVM", "secondMainScreenSpeakerId: $it")
            it?: return@observe
            binding.mainScreenTwo.setupMainScreen(it)
        }
        viewModel.myVideoIsTurnedOn.observe(viewLifecycleOwner) {
            it?: return@observe
            val screen = if (viewModel.mainScreenSpeakerId.value == viewModel.user.id) binding.mainScreen
            else if(viewModel.secondMainScreenSpeakerId.value == viewModel.user.id) binding.mainScreenTwo
            else return@observe
            screen.setupMainScreen(viewModel.user.id)
        }
        viewModel.showSecondVideoSurface.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: showSecondVideoSurface $it")
            binding.mainScreenTwo.showVideo = it
        }
        viewModel.isLikeLoading.observe(viewLifecycleOwner){
            Log.d("LIKE_ENABLED", "observe: $it")
        }
    }

    override fun onSecondResume() {
        viewModel.mainScreenSpeakerId.value?.let { mss ->
            binding.mainScreen.setupMainScreen(mss)
        }
        viewModel.secondMainScreenSpeakerId.value?.let { mss ->
            binding.mainScreenTwo.setupMainScreen(mss)
        }
    }

    override fun onAboutToJoin() {
        if (viewModel.iAmManager.value==true) return
        MaterialAlertDialogBuilder(requireContext())
            .setTitle(R.string.podium_interview_about_title)
            .setMessage(R.string.podium_interview_about_desc)
            .setPositiveButton(R.string.common_close) { dialog, which ->
                dialog.dismiss()
            }
            .show()
    }

    private lateinit var liveChatEmptyView: LayoutListStateEmptyBinding
    private lateinit var chatTextBoxEmptyView: LayoutPodiumChatPausedEmptyBinding

    fun setEmptyView() {
        val emptyView: View = binding.liveChatMultiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        liveChatEmptyView = LayoutListStateEmptyBinding.bind(emptyView).apply {
            prepare(message = R.string.podium_comments_disabled)
        }

        val textBoxEmptyView: View = binding.chatTextBoxMultiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        chatTextBoxEmptyView = LayoutPodiumChatPausedEmptyBinding.bind(textBoxEmptyView)
    }

    override fun showMoreMenu(v: View): PopupMenu {
        return super.showMoreMenu(v).apply {
            menu.apply {
                findItem(R.id.action_pause_resume_mic).isVisible = false
                findItem(R.id.action_close).isVisible =  viewModel.iAmManager.value == true  ||viewModel.iHavePowerToEndPodium || (viewModel.isAdmin(viewModel.user.id) && !viewModel.isManagerAvailable())
            }
        }
    }

    override fun toAdminList() {
        val action = PodiumLiveInterviewFragmentDirections.actionPodiumLiveInterviewFragmentToPodiumAdminsBottomSheetFragment()
        findNavController().navigateSafe(action)
    }

    override fun toLiveChatActions(userId: Int) {
        val action = PodiumLiveInterviewFragmentDirections.actionPodiumLiveInterviewFragmentToPodiumLiveChatActionsBottomSheetFragment(userId)
        findNavController().navigateSafe(action)
    }

    override fun toBuyCamera(buy: Boolean) {
        val action = PodiumLiveInterviewFragmentDirections.actionPodiumLiveInterviewFragmentToPodiumBuyCameraBottomSheetFragment(buy)
        findNavController().navigateSafe(action)
    }

    override fun toLiveUsersList() {
        val action = PodiumLiveInterviewFragmentDirections.actionPodiumLiveInterviewFragmentToPodiumLiveUsersListBottomSheetFragment()
        findNavController().navigateSafe(action)
    }

    override fun toInvitations() {
        val action = PodiumLiveInterviewFragmentDirections.actionPodiumLiveInterviewFragmentToPodiumInviteBottomSheetFragment(podiumId = podiumIdArg)
        findNavController().navigateSafe(action)
    }

    override fun toRestrictedUsers() {
        val action = PodiumLiveInterviewFragmentDirections.actionPodiumLiveInterviewFragmentToPodiumBlockedUsersBottomSheetFragment()
        findNavController().navigateSafe(action)
    }
}
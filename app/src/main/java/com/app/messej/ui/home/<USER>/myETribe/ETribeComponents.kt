package com.app.messej.ui.home.publictab.myETribe

import android.content.Context
import android.content.DialogInterface
import androidx.annotation.ColorRes
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.toUpperCase
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.core.content.ContextCompat
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.app.messej.R
import com.app.messej.data.model.api.eTribe.ETribeResponse
import com.app.messej.data.model.enums.ETribeBannerView
import com.app.messej.data.model.enums.ETribeTabs
import com.app.messej.data.model.enums.UserBadge
import com.app.messej.ui.composeComponents.ComposeTextField
import com.app.messej.ui.composeComponents.ComposeTextFieldState
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.composeComponents.ShimmerDefaultCircleItem
import com.app.messej.ui.composeComponents.ShimmerDefaultItem
import com.app.messej.ui.composeComponents.UserBadge
import com.app.messej.ui.home.publictab.myETribe.ETribeUtil.setupETribeCitizenshipTextColor
import com.app.messej.ui.home.publictab.myETribe.ETribeUtil.setupText
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomHorizontalSpacer
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer
import com.app.messej.ui.utils.EnumUtils.displayText
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import kotlin.math.roundToInt

/**
Used for button for switching the e-tribe tabs.
*/
@Composable
fun ETribeSelectionButton(
    modifier: Modifier = Modifier,
    tab: ETribeTabs,
    count:Int?,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val context = LocalContext.current
    Row(modifier = modifier
        .background(
            color = colorResource(id = if (isSelected) R.color.colorPrimary else R.color.textInputBackground), shape = RoundedCornerShape(size = 5.dp)
        )
        .clickable { onClick() }
        .padding(all = dimensionResource(id = R.dimen.element_spacing)),
        horizontalArrangement = Arrangement.Center
    ) {
        Text(
            text = buildAnnotatedString {
                append(context.getString(tab.setupText()))
                withStyle(style = SpanStyle(color = colorResource(id = if (isSelected) R.color.white else
                    when(tab) {
                        ETribeTabs.All -> R.color.textColorSecondary
                        ETribeTabs.Active -> R.color.colorPass
                        ETribeTabs.Inactive -> R.color.colorError
                    }
                ))) { append(" (${count ?: 0})") }
            }.toUpperCase(),
            color = colorResource(id = if (isSelected) R.color.white else R.color.textColorSecondary),
            style = FlashatComposeTypography.defaultType.button
        )
    }
}

/**
E-Tribe top portion banner view
 */
@Composable
fun ETribeBannerView(item: ETribeResponse.SuperStarTribeDetail?, onClick: (Int) -> Unit) {
    val roundedCornerShape = RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing))
    Row(modifier = Modifier
        .border(color = colorResource(id = R.color.colorAlwaysLightPrimary), shape = roundedCornerShape, width = 1.dp)
        .background(color = colorResource(id = R.color.colorAlwaysLightSecondary), shape = roundedCornerShape)
        .clickable { item?.superStarTribeId?.let { onClick(it) } }
        .padding(all = dimensionResource(id = R.dimen.element_spacing))
        .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        ETribeThumbnailView(imageUrl = item?.superStarTribeImage)
        Text(
            modifier = Modifier
                .weight(weight = 1F)
                .padding(horizontal = dimensionResource(id = R.dimen.activity_margin)),
            style = FlashatComposeTypography.defaultType.caption,
            text = buildAnnotatedString {
                append(stringResource(id = R.string.e_tribe_super_star_tribe_title_first))
                withStyle(style = SpanStyle(fontWeight = FontWeight.W700, color = colorResource(id = R.color.colorAlwaysLightPrimary))) { append(" ${item?.tribeName ?: ""}\n") }
                append(stringResource(id = R.string.e_tribe_super_star_tribe_title_second))
                withStyle(style = SpanStyle(fontWeight = FontWeight.W700)) { append(" ${item?.superStarName ?: ""}") }
            }
        )
        Icon(
            painter = painterResource(id = R.drawable.ic_round_arrow_right),
            tint = Color.Unspecified,
            contentDescription = null
        )
    }
}

@Composable
fun ETribeDeletedAndFreeUserBannerView(type: ETribeBannerView?) {
    val iconAndTextColor = colorResource(id = if (type == ETribeBannerView.SuperStarTribeDeletedByAdminView) R.color.colorError else R.color.textColorOnSecondary)
    Row(modifier = Modifier
        .background(
            color = if (type == ETribeBannerView.SuperStarTribeDeletedByAdminView)
                colorResource(id = R.color.colorError).copy(alpha = 0.25F)
            else colorResource(id = R.color.textColorAlwaysDarkSecondary),
            shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing)))
        .padding(all = dimensionResource(id = R.dimen.activity_margin))
        .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Icon(painter = painterResource(id = if (type == ETribeBannerView.SuperStarTribeDeletedByAdminView) R.drawable.ic_trash_open else R.drawable.ic_cross),
             tint = iconAndTextColor,
             contentDescription = null
        )
        CustomHorizontalSpacer(space = dimensionResource(id = R.dimen.element_spacing))
        Text(
            style = FlashatComposeTypography.overLineBold,
            text = stringResource(id = if (type == ETribeBannerView.SuperStarTribeDeletedByAdminView) R.string.e_tribe_super_star_tribe_admin_deleted_text
            else R.string.e_tribe_super_star_free_user_text),
            color = iconAndTextColor
        )
    }
}

/**
Used this for represent single item in the list
 */
@Composable
fun ETribeSingleItem(
    modifier: Modifier = Modifier,
    item: ETribeResponse.ETribeMembers?,
    onUserProfileClick: (Int) -> Unit = {}
) {
    Row(modifier = modifier
        .fillMaxWidth()
        .height(intrinsicSize = IntrinsicSize.Max)) {
        ETribeUserView(
            userImageUrl = item?.thumbnail,
            isActive= item?.isOnline == true,
            lastSeenTime = item?.lastSeenHumanized(LocalContext.current),
            userType = item?.userBadge,
            onProfileClick = { item?.id?.let { userId -> onUserProfileClick(userId) } }
        )
        Column(modifier = Modifier
            .padding(start = dimensionResource(id = R.dimen.element_spacing))
            .fillMaxWidth()
            .weight(weight = 0.6F)
        ) {
            //User name and flag view
            Row {
                Text(
                    text = item?.name ?: "",
                    style = FlashatComposeTypography.overLineBold,
                    color = colorResource(id = R.color.textColorPrimary)
                )
                //Used for flag
                item?.countryFlag?.let {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .crossfade(enable = true)
                            .data(data = it)
                            .build(),
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .padding(start = dimensionResource(id = R.dimen.element_spacing))
                            .size(width = 20.dp, height = 13.dp)
                    )
                }
            }
            // User citizenship and GNR, Rating icon text view
            Row(
                horizontalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.line_spacing)),
                modifier = Modifier.padding(top = dimensionResource(id = R.dimen.line_spacing)),
                verticalAlignment = Alignment.CenterVertically
            ) {
                //Citizenship Text
                item?.citizenship?.let {
                    Text(
                        text = stringResource(id = it.displayText()),
                        style = FlashatComposeTypography.overLineSmallerBold,
                        color = colorResource(id = it.setupETribeCitizenshipTextColor()),
                        modifier = Modifier
                            .setupCitizenshipTextBackground(citizenShip = it)
                            .padding(horizontal = dimensionResource(id = R.dimen.line_spacing))
                    )
                }
                //GNR View
                ETribeGNRRatingView(isGnr = true, value = item?.contributorLevel)
                // Rating View
                ETribeGNRRatingView(isGnr = false, value = stringResource(id = R.string.common_percentage_value, "${(item?.userRating ?: 0.0).roundToInt()}"))
            }
        }
        //E Tribe Active Inactive count view
        Divider(
            color = colorResource(id = R.color.colorDivider),
            thickness = 0.5.dp,
            modifier = Modifier
                .padding(horizontal = dimensionResource(id = R.dimen.line_spacing))
                .width(width = 1.dp)
                .fillMaxHeight()
        )
        ETribeOnlineOfflineView(
            modifier = Modifier
                .fillMaxWidth()
                .weight(weight = 0.4F),
            tribeDetail = item?.memberTribeDetails
        )
    }
}

/**
Used for showing E-tribe thumbnail and crown icon view
 */
@Composable
fun ETribeThumbnailView(imageUrl: String?) {
    Box {
        // E Tribe thumbnail
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(data = imageUrl)
                .crossfade(enable = true)
                .build(),
            placeholder = painterResource(R.drawable.im_group_placeholder),
            error = painterResource(R.drawable.im_group_placeholder),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .size(size = 30.dp)
                .clip(RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing)))
        )
        // E Tribe Crown Icon
        Icon(
            painter = painterResource(id = R.drawable.ic_huddle_crown),
            tint = Color.Unspecified,
            modifier = Modifier
                .size(size = 10.dp)
                .align(alignment = Alignment.TopEnd)
                .offset(y = (-2).dp, x = 2.dp),
            contentDescription = null
        )
    }
}

/**
Used for showing GNR and Rating view
 */
@Composable
fun ETribeGNRRatingView(isGnr:Boolean, value: String?) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Icon(
            painter = painterResource(id = if (isGnr) R.drawable.ic_idcard_lv else R.drawable.ic_idcard_rating),
            tint = Color.Unspecified,
            modifier = Modifier.size(size = 20.dp),
            contentDescription = null
        )
        Text(
            text = buildAnnotatedString {
                withStyle(style = SpanStyle(fontWeight = FontWeight.Bold)) { append("${value ?: "0"}\n") }
                withStyle(style = SpanStyle(fontSize = 6.sp)) { append(stringResource(id = if (isGnr) R.string.id_card_lv else R.string.id_card_rating)) }
            }, modifier = Modifier.padding(start = dimensionResource(id = R.dimen.line_spacing)),
            style = FlashatComposeTypography.overLineSmaller.copy(lineHeight = 8.sp),
            color = colorResource(id = R.color.colorPrimaryDark)
        )
    }
}

@Preview
@Composable
private fun ETribeGNRRatingPreview() {
    ETribeGNRRatingView(isGnr = false, value = "10%")
}

/**
Used for showing Total e-tribe count, active, inactive users count (Right side of the list)
 */

@Composable
fun ETribeOnlineOfflineView(modifier: Modifier = Modifier, tribeDetail: ETribeResponse.MemberTribeDetails?) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = colorResource(id = R.color.colorSurfaceSecondary), shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing))
            )
            .padding(bottom = dimensionResource(id = R.dimen.line_spacing))
    ) {
        Row(modifier = Modifier
            .padding(start = dimensionResource(id = R.dimen.element_spacing))
            .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            //Total tribe users count
            TribeUsersCountView(
                iconColor = R.color.colorPrimary,
                count = tribeDetail?.totalUsers
            )
            // e-Tribe text -> right side view
            Text(
                text = stringResource(id = R.string.e_tribe),
                style = FlashatComposeTypography.overLineSmallerItalic,
                color = colorResource(id = R.color.colorPrimary),
                modifier = Modifier
                    .clip(
                        shape = RoundedCornerShape(
                            bottomEnd = dimensionResource(id = R.dimen.line_spacing), bottomStart = dimensionResource(id = R.dimen.line_spacing)
                        )
                    )
                    .background(color = colorResource(id = R.color.colorAlwaysLightPrimaryLighter).copy(alpha = 0.2F))
                    .padding(horizontal = dimensionResource(id = R.dimen.line_spacing))
            )
        }
        Spacer(modifier = Modifier.height(height = dimensionResource(id = R.dimen.element_spacing)))
        Row(
            modifier = Modifier.padding(horizontal = dimensionResource(id = R.dimen.element_spacing)),
            verticalAlignment = Alignment.CenterVertically
        ) {
            ETribeThumbnailView(imageUrl = tribeDetail?.thumbnailUrl)
            Spacer(modifier = Modifier.width(width = dimensionResource(id = R.dimen.element_spacing)))
            // Inactive count view
            TribeUsersCountView(
                iconColor = R.color.colorError,
                count = tribeDetail?.inactiveUsers
            )
            Text(
                modifier = Modifier.padding(horizontal = dimensionResource(id = R.dimen.element_spacing)),
                text = "/",
                style = FlashatComposeTypography.overLineSmallerBold,
                color = colorResource(id = R.color.colorChatTextPrimary)
            )
            // Active count view
            TribeUsersCountView(
                iconColor = R.color.colorPass,
                count = tribeDetail?.activeUsers
            )
        }
    }
}


/**
Used for showing dot icon and users total count view
*/
@Composable
fun TribeUsersCountView(
    @ColorRes iconColor: Int,
    count: Int?
) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Box(
            modifier = Modifier
                .border(width = 0.5.dp, shape = CircleShape, color = colorResource(id = iconColor))
                .size(size = 10.dp), contentAlignment = Alignment.Center
        ) {
            Box(
                modifier = Modifier
                    .clip(shape = CircleShape)
                    .size(size = 5.dp)
                    .background(color = colorResource(id = iconColor))
            )
        }
        Text(
            modifier = Modifier.padding(start = dimensionResource(id = R.dimen.line_spacing)),
            text = "$count",
            style = FlashatComposeTypography.overLineSmallerBold, color = colorResource(id = R.color.colorChatTextPrimary)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun TribeUserCountPreview() {
    TribeUsersCountView(
        iconColor = R.color.colorPrimary,
        count = 10
    )
}

/**
Used for Showing User Image, User Badge, and online status
 */
@Composable
fun ETribeUserView(
    userImageUrl: String?,
    isActive: Boolean,
    lastSeenTime: String?,
    userType: UserBadge?,
    onProfileClick: () -> Unit = {}
) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Box {
            // User Image View
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .crossfade(enable = true)
                    .data(data = userImageUrl)
                    .build(),
                placeholder = painterResource(R.drawable.im_user_placeholder_square),
                error = painterResource(R.drawable.im_user_placeholder_square),
                contentDescription = null,
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .size(size = 30.dp)
                    .clip(CircleShape)
                    .clickable { onProfileClick() }
            )
            // User Badge View
            UserBadge(
                modifier = Modifier.align(alignment = Alignment.TopStart),
                userType = userType
            )
            if (isActive) {
                Box(modifier = Modifier
                    .align(alignment = Alignment.BottomEnd)
                    .border(width = 0.5.dp, color = Color.White, shape = CircleShape)
                    .background(color = colorResource(id = R.color.colorPass), shape = CircleShape)
                    .size(size = 9.dp)
                )
            }
        }
        // Recently active time
        if (!isActive && !lastSeenTime.isNullOrEmpty()) {
            Text(
                text = lastSeenTime,
                modifier = Modifier.padding(top = dimensionResource(id = R.dimen.line_spacing)),
                style = FlashatComposeTypography.overLineTiny,
                color = colorResource(id = R.color.colorChatTextPrimary)
            )
        }
    }
}

@Preview
@Composable
private fun ETribeUserPreview() {
    ETribeUserView(
        userImageUrl = null,
        isActive= true,
        lastSeenTime = "4.30 pm",
        userType = UserBadge.PREMIUM,
    )
}

@Composable
fun EditETribeAlertView(
    state: ComposeTextFieldState,
    onCancelClick: () -> Unit,
    onSaveClick: () -> Unit
) {
    Dialog(
        onDismissRequest = { },
        properties = DialogProperties(dismissOnClickOutside = false, dismissOnBackPress = true)
    ) {
        val keyboardManager = LocalSoftwareKeyboardController.current
        val focusManager = LocalFocusManager.current

        Column(
            modifier = Modifier
                .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
                .background(color = colorResource(id = R.color.colorSurfaceSecondaryDark))
                .padding(all = dimensionResource(id = R.dimen.activity_margin)),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(id = R.string.e_tribe_edit_title),
                style = FlashatComposeTypography.defaultType.subtitle2,
                color = colorResource(id = R.color.colorPrimary)
            )
            CustomVerticalSpacer(space = dimensionResource(id = R.dimen.element_spacing))
            ComposeTextField(
                state = state,
                placeHolderText = stringResource(id = R.string.e_tribe_enter_tribe_name)
            )
            CustomVerticalSpacer(space = dimensionResource(id = R.dimen.element_spacing))
            Row(modifier = Modifier.align(alignment = Alignment.End)) {
                Text(modifier = Modifier.clickable {
                    focusManager.clearFocus(force = true)
                    keyboardManager?.hide()
                    onCancelClick()
                    },
                    text = stringResource(id = R.string.common_cancel),
                    style = FlashatComposeTypography.overLineBold,
                    color = colorResource(id = R.color.colorError)
                )
                Text(
                    modifier = Modifier
                        .padding(start = dimensionResource(id = R.dimen.activity_margin))
                        .clickable(
                            enabled = !state.text.isNullOrEmpty() && state.text?.isNotBlank() == true
                        ) {
                            focusManager.clearFocus(force = true)
                            keyboardManager?.hide()
                            onSaveClick()
                          },
                    text = stringResource(id = R.string.common_save),
                    style = FlashatComposeTypography.overLineBold,
                    color = colorResource(id = if (state.text.isNullOrEmpty() || state.text?.isBlank() == true) R.color.colorAlwaysLightPrimaryLighter else R.color.colorPrimary)
                )
            }
        }
    }
}

@Composable
fun ETribeShimmerSingleItem(brush: Brush) {
    Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
        ShimmerDefaultCircleItem(brush = brush, size = 40.dp)
        CustomHorizontalSpacer(space = dimensionResource(id = R.dimen.element_spacing))
        Column(modifier = Modifier.fillMaxWidth().weight(weight = 0.6F).padding(end = dimensionResource(id = R.dimen.activity_margin))) {
            ShimmerDefaultItem(fraction = 0.8F, brush = brush)
            CustomVerticalSpacer(space = dimensionResource(id = R.dimen.element_spacing))
            ShimmerDefaultItem(fraction = 0.35F, brush = brush)
        }
        ShimmerDefaultItem(brush = brush, fraction = 0.4F, height = 40.dp, cornerSize = 8.dp)
    }
}

fun showETribeSupperStarAlert(superStarName: String?, message: String, context: Context) {
    val alertDialog = MaterialAlertDialogBuilder(context)
        .setTitle(context.getString(R.string.e_tribe_super_star_message_title, superStarName ?: ""))
        .setMessage(message)
        .setPositiveButton(context.getString(R.string.common_close)) { _, _ -> }
        .create()
    alertDialog.show()
    val positiveButton = alertDialog.getButton(DialogInterface.BUTTON_POSITIVE)
    positiveButton.setTextColor(ContextCompat.getColor(context, R.color.colorError))
}

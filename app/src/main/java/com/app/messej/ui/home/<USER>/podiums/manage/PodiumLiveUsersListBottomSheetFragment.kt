package com.app.messej.ui.home.publictab.podiums.manage

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.isVisible
import androidx.lifecycle.MutableLiveData
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.enums.ChallengeContributionType
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.TheaterJoinType
import com.app.messej.data.model.enums.UserType
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PodiumLiveUsersListBottomSheetFragment : PodiumLiveUsersListBaseFragment() {

    private val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observe()
    }

    override fun refreshData() {

    }

    @SuppressLint("NotifyDataSetChanged")
    fun observe() {
        Log.d("PLULDS", "observe live users")
        viewModel.liveUsersList.observe(viewLifecycleOwner){pagingData ->
            pagingData?.let {
                mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
            }
        }

        viewModel.onFollowedUser.observe(viewLifecycleOwner){
            mAdapter?.refresh()
            Toast.makeText(requireContext(), getString(R.string.public_star_following_text, it), Toast.LENGTH_SHORT).show()
        }

        viewModel.onUnfollowedUser.observe(viewLifecycleOwner){
            mAdapter?.refresh()
            Toast.makeText(requireContext(), getString(R.string.public_star_unfollowing_text, it), Toast.LENGTH_SHORT).show()
        }

        viewModel.allSpeakers.observe(viewLifecycleOwner) {
            mAdapter?.notifyDataSetChanged()
        }

        viewModel.liveUsersListLoading.observe(viewLifecycleOwner) {
            binding.actionLoading.isVisible = it
        }

        viewModel.hasSpaceForNewSpeaker.observe(viewLifecycleOwner) {
            Log.w("PLUBSF", "observe:hasSpaceForNewSpeaker: $it ", )
        }

        viewModel.podiumKind.observe(viewLifecycleOwner) {
            it?: return@observe
        }
        viewModel.iAmManager.observe(viewLifecycleOwner) {
            it?: return@observe
        }
        viewModel.iAmElevated.observe(viewLifecycleOwner) {
            it?: return@observe
        }

        viewModel.onInviteToSpeakRequested.observe(viewLifecycleOwner) {
            mAdapter?.notifyDataSetChanged()
        }

        viewModel.onInviteToSpeakError.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
        }

        viewModel.onInviteToSpeakCancelled.observe(viewLifecycleOwner) {
            mAdapter?.notifyDataSetChanged()
        }

        viewModel.onInviteToSpeakCancelError.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
        }
    }

    override val loadingStateLiveData: MutableLiveData<Boolean>
        get() = viewModel.liveUsersListLoading

    override val actionListener: PodiumLiveUsersListAdapter.PodiumActionListener
        get() = object : PodiumLiveUsersListAdapter.PodiumActionListener {
            override fun onMenuClick(view: View, item: PodiumParticipant) {
//                if(!shouldShowMenu(item)) return
                val popup = PopupMenu(view.context, view)
                popup.inflate(R.menu.menu_podium_live_user)
                val isNotSelf = viewModel.user.id != item.id
                val wasInvited = viewModel.findSpeakerInvite(item.id)!=null
                popup.menu.apply {
                    val canInvite = canInviteUser(item)

                    findItem(R.id.send_challenge_invite).apply {
                        title = if (wasInvited) getString(R.string.podium_invite_to_speak_cancel) else getString(R.string.podium_invite_to_speak)
                        isVisible = canInvite && ((viewModel.podium.value?.audienceFee ?: 0) == 0)
                    }

                    if (((viewModel.podium.value?.audienceFee ?: 0) != 0) &&
                        viewModel.podium.value?.kind != PodiumKind.THEATER &&
                        canInvite && viewModel.podium.value?.isManager == true && !viewModel.isAdmin(item.id)
                    ) {
                        findItem(R.id.send_invite_audience_free).isVisible = true
                        findItem(R.id.send_invite_audience_with_fee).isVisible = true
                    }

                    findItem(R.id.follow_user).apply{
                        title = if (item.isFollowed == true) getString(R.string.user_action_unfollow) else getString(R.string.user_action_follow)
                        val isMySuperstar = viewModel.user.superStarId == item.id
                        isVisible = isNotSelf && !isMySuperstar
                    }
                    findItem(R.id.send_gift).apply{
                        isVisible = viewModel.canSendGiftsTo(item)
                    }
                    if (viewModel.podiumKind.value==PodiumKind.THEATER) {
                        findItem(R.id.send_challenge_invite).apply {
                            isVisible = canInvite && wasInvited
                        }
                        findItem(R.id.send_invite_stage).apply {
                            val eligible = item.membership==UserType.PREMIUM
                            isVisible = canInvite && eligible && viewModel.secondMainScreenSpeakerId.value==null
                        }
                        findItem(R.id.send_invite_audience).apply {
                            val eligible = (item.userRating?:0.0)>=0.9 || item.membership==UserType.PREMIUM
                            isVisible = canInvite && eligible && viewModel.hasSpaceForNewSpeakerExcludingMainScreens()
                        }
                    }
                }

                popup.setOnMenuItemClickListener { menuItem ->
                    fun confirmSpeakInvite(confirm: () -> Unit) {
                        if (canInviteUser(item)) {
                            if (viewModel.hasSpaceForNewSpeaker.value == true) {
                                confirmAction(
                                    message = getString(R.string.podium_invite_speaker_confirm_message), positiveTitle = R.string.common_confirm, negativeTitle = R.string.common_cancel
                                ) {
                                    confirm.invoke()
                                }
                            } else {
                                showToast(getString(R.string.podium_max_speakers_toast, viewModel.podiumKind.value?.maxSpeakers?.toString().orEmpty()))
                            }
                        } else {
                            popup.dismiss()
                        }
                    }
                    when (menuItem.itemId) {
                        R.id.send_challenge_invite -> {
                            if (wasInvited != (viewModel.findSpeakerInvite(item.id) != null)) {
                                return@setOnMenuItemClickListener true
                            }
                            return@setOnMenuItemClickListener if (wasInvited) {
                                viewModel.cancelInviteToSpeak(item.id)
                                true
                            } else {
                                confirmSpeakInvite {
                                    viewModel.inviteToSpeak(item.id)
                                }
                                true
                            }
                        }

                        R.id.send_invite_stage -> {
                            if (wasInvited != (viewModel.findSpeakerInvite(item.id) != null)) {
                                return@setOnMenuItemClickListener true
                            }
                            confirmSpeakInvite {
                                viewModel.inviteToSpeak(item.id,TheaterJoinType.STAGE)
                            }
                        }
                        R.id.send_invite_audience -> {
                            if (wasInvited != (viewModel.findSpeakerInvite(item.id) != null)) {
                                return@setOnMenuItemClickListener true
                            }
                            confirmSpeakInvite {
                                viewModel.inviteToSpeak(userId = item.id,)
                            }
                        }

                        R.id.send_invite_audience_free -> {
                            confirmSpeakInvite {
                                viewModel.inviteToSpeak(userId = item.id, invitedForFree = true)
                            }
                        }

                        R.id.send_invite_audience_with_fee -> {
                            confirmSpeakInvite {
                                viewModel.inviteToSpeak(userId = item.id, invitedForFree = false)
                            }
                        }

                        R.id.follow_user -> {
                            viewModel.toggleFollow(item.id, item.name, item.isFollowed == true)
                        }
                        R.id.send_gift -> {
                            viewModel.podium.value?.let {
                                val action = PodiumSpeakerActionsBottomSheetFragmentDirections.actionGlobalGiftFragment(item.id, giftContext = GiftContext.GIFT_PODIUM, managerId = it.managerId, giftContextId = it.id)
                                findNavController().navigateSafe(action,navOptions = NavOptions.Builder()
                                    .setPopUpTo(R.id.podiumSpeakerActionsBottomSheetFragment, inclusive = true)
                                    .build())
                            }
                        }
                    }
                    return@setOnMenuItemClickListener true
                }
                popup.show()
            }

            override fun canInviteUser(item: PodiumParticipant): Boolean {
                val challenge = viewModel.activeChallenge.value
                val notBlockedByChallenge = if (challenge != null) {
                    challenge.status == PodiumChallenge.ChallengeStatus.SETUP
                            && challenge.contributorType != ChallengeContributionType.SPEAKERS
                } else true

                val iAmManager = viewModel.iAmManager.value == true
                val iAmElevated = viewModel.iAmElevated.value == true

                val isNotSelf = viewModel.user.id != item.id
                val notASpeaker = viewModel.getSpeaker(item.id)==null
                val userCanBeInvited = isNotSelf && notASpeaker

                val canInvite = when(viewModel.podiumKind.value) {
                    PodiumKind.LECTURE -> iAmManager && notBlockedByChallenge
                    PodiumKind.ASSEMBLY -> iAmManager
                    PodiumKind.INTERVIEW -> iAmManager
                    PodiumKind.THEATER -> iAmElevated
                    PodiumKind.ALONE -> false
                    PodiumKind.MAIDAN -> false
                    null -> false
                }

                Log.d("SHSHMEN","${viewModel.podiumKind}")

                return canInvite && userCanBeInvited
            }

            override fun isInvited(item: PodiumParticipant): Boolean {
                return viewModel.findSpeakerInvite(item.id)!=null
            }

            override fun onFollowClick(item: PodiumParticipant) {
                viewModel.toggleFollow(item.id, item.name, item.isFollowed == true)
            }

            override fun navigateToIdCard(item: PodiumParticipant) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(item.id))
            }


            override fun isSpeaker(item: PodiumParticipant): Boolean {
                val speaker = viewModel.getSpeaker(item.id)
                return speaker!=null && !speaker.isManager
            }

            override fun isNotSelf(item: PodiumParticipant): Boolean {
                return viewModel.user.id != item.id
            }

            override fun shouldShowLikesAndRating(item: PodiumParticipant): Boolean {
                return viewModel.podiumKind.value==PodiumKind.THEATER
            }
        }
}
package com.app.messej.ui.home.publictab.authorities.stateAffairs

import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.paging.LoadState
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.enums.StateAffairsTypes
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserProfileContext
import com.app.messej.databinding.FragmentStateAffairsMainBinding
import com.app.messej.databinding.LayoutStateAffairLowGradedUsersBinding
import com.app.messej.ui.home.publictab.authorities.stateAffairs.adapters.StateAffairCitizenshipUserPagingAdapter
import com.app.messej.ui.home.publictab.authorities.stateAffairs.adapters.StateAffairFlashatersPagingAdapter
import com.app.messej.ui.home.publictab.authorities.stateAffairs.adapters.StateAffairTribePagingAdapter
import com.app.messej.ui.profile.PublicUserProfileFragmentArgs
import com.app.messej.ui.profile.PublicUserProfileViewModel
import com.app.messej.ui.utils.BindingExtensions.setData
import com.app.messej.ui.utils.BindingExtensions.setupIDCard
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView

class StateAffairsMainFragment : Fragment(),StateAffairFlashatersPagingAdapter.stateAffairActionListener,StateAffairCitizenshipUserPagingAdapter.stateAffairActionListener,StateAffairTribePagingAdapter.stateAffairActionListener {


    private lateinit var binding: FragmentStateAffairsMainBinding
    private val viewModel: StateAffairsMainViewModel by  navGraphViewModels(R.id.nav_graph_state_affairs)
    private  var strongestTribeAdapter: StateAffairTribePagingAdapter? = null
    private var flashatMinistersAdapter: StateAffairCitizenshipUserPagingAdapter? = null
    private var flashatAmbassadorsAdapter: StateAffairCitizenshipUserPagingAdapter? = null
    private var flashatOfficersAdapter: StateAffairCitizenshipUserPagingAdapter? = null

    private var skillFullFlashatersAdapter: StateAffairFlashatersPagingAdapter? = null
    private var popularFlashatersAdapter: StateAffairFlashatersPagingAdapter? = null
    private var generousFlashatersAdapter: StateAffairFlashatersPagingAdapter? = null

    private val userViewModel: PublicUserProfileViewModel by viewModels()
    private lateinit var userIdCardArgs:  PublicUserProfileFragmentArgs



    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_state_affairs_main, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolBarTitle.text  = getString(R.string.state_affair_title)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }
    fun setUp(){
        setAdapter()
        viewAllClickActions()
        viewModel.getStateStateStatistics()
    }

    private fun viewAllClickActions() {
        binding.strongestTribes.textViewAll.setOnClickListener {
            gotoViewAll(StateAffairsTypes.STRONGEST_TRIBES)
        }
        binding.flashatMinisters.textViewAll.setOnClickListener {
            gotoViewAll(StateAffairsTypes.FLASHAT_MINISTERS)
        }
        binding.flashatAmbassadors.textViewAll.setOnClickListener {
            gotoViewAll(StateAffairsTypes.FLASHAT_AMBASSADORS)
        }
        binding.flashatOfficers.textViewAll.setOnClickListener {
            gotoViewAll(StateAffairsTypes.FLASHAT_OFFICERS)
        }
        binding.mostGenerous.textViewAll.setOnClickListener {
            gotoViewAll(StateAffairsTypes.MOST_GENEROUS_FLASHATERS)
        }
        binding.mostPopular.textViewAll.setOnClickListener {
            gotoViewAll(StateAffairsTypes.MOST_POPULAR_FLASHATERS)
        }
        binding.mostSkillFull.textViewAll.setOnClickListener {
            gotoViewAll(StateAffairsTypes.MOST_SKILLFUL_FLASHATERS)
        }
        binding.stateStatistics.divider.visibility =View.GONE
        binding.stateStatistics.textViewAll.visibility =View.GONE
        binding.stateStatistics.imageViewAll.visibility =View.GONE

        binding.stateStatistics.cardVisitor.setOnClickListener {
            viewModel.StateStateStatistics.value?.let { data -> goToCount(data, UserCitizenship.VISITOR, requireContext()) }
        }
        binding.stateStatistics.cardResident.setOnClickListener {
            viewModel.StateStateStatistics.value?.let { data -> goToCount(data, UserCitizenship.RESIDENT, requireContext()) }
        }
        binding.stateStatistics.cardCitizen.setOnClickListener {
            viewModel.StateStateStatistics.value?.let { data -> goToCount(data, UserCitizenship.CITIZEN, requireContext()) }
        }
        binding.stateStatistics.cardGolden.setOnClickListener {
            viewModel.StateStateStatistics.value?.let { data -> goToCount(data, UserCitizenship.GOLDEN, requireContext()) }
        }
    }

    private fun gotoViewAll(types: StateAffairsTypes) {
        findNavController().navigateSafe(
            StateAffairsMainFragmentDirections.actionStateAffairsFragmentToStateAffairListViewAllFragment(types)
        )
    }

    private fun navigateToUserProfile(userId: Int?) {
        userId?.let {
            findNavController().navigateSafe(
                NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(it, false)
            )
        }
    }

    private fun setAdapter() {
        setTribesAdapter() /**strongest Tribe*/
        setFlashatMinistersAdapter() /**Flashat minister*/
        setFlashatAmbassadorAdapter() /**Flashat ambassadors*/
        setFlashatOfficersAdapter() /**Flashat officers*/
        setskillfullFlashatersAdapter() /**skillFull flashaters*/
        setPopularFlashatersAdapter() /**popular flashaters*/
        setGenerousFlashatersAdapter() /**generous flashaters*/

    }

    private fun setGenerousFlashatersAdapter() {
        generousFlashatersAdapter = StateAffairFlashatersPagingAdapter(false, StateAffairsTypes.MOST_GENEROUS_FLASHATERS,this)

        binding.mostGenerous.listRecycler.apply {
            setHasFixedSize(true)
            adapter = generousFlashatersAdapter
        }

        generousFlashatersAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.mostGenerous.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.mostGenerous.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_state_affair_empty)
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.state_affair_empty_generous_members)
        }
    }
    private fun setPopularFlashatersAdapter() {
        popularFlashatersAdapter = StateAffairFlashatersPagingAdapter(false, StateAffairsTypes.MOST_POPULAR_FLASHATERS,this)

        binding.mostPopular.listRecycler.apply {
            setHasFixedSize(true)
            adapter = popularFlashatersAdapter
        }

        popularFlashatersAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.mostPopular.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.mostSkillFull.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_state_affair_empty)
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.state_affair_empty_popular_members)
        }
    }

    private fun setskillfullFlashatersAdapter() {
        skillFullFlashatersAdapter = StateAffairFlashatersPagingAdapter(false, StateAffairsTypes.MOST_SKILLFUL_FLASHATERS,this)

        binding.mostSkillFull.listRecycler.apply {
            setHasFixedSize(true)
            adapter = skillFullFlashatersAdapter
        }

        skillFullFlashatersAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.mostSkillFull.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.mostSkillFull.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_state_affair_empty)
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.state_affair_empty_skillful_members)
        }
    }

    private fun setFlashatOfficersAdapter() {
        flashatOfficersAdapter = StateAffairCitizenshipUserPagingAdapter(false,this)

        binding.flashatOfficers.listRecycler.apply {
            setHasFixedSize(true)
            adapter = flashatOfficersAdapter
        }

        flashatOfficersAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.flashatOfficers.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.flashatOfficers.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_state_affair_empty)
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.state_affair_empty_officer_users)
        }
    }

    private fun setFlashatAmbassadorAdapter() {
        flashatAmbassadorsAdapter = StateAffairCitizenshipUserPagingAdapter(false,this)

        binding.flashatAmbassadors.listRecycler.apply {
            setHasFixedSize(true)
            adapter = flashatAmbassadorsAdapter
        }

        flashatAmbassadorsAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.flashatAmbassadors.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.flashatAmbassadors.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_state_affair_empty)
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.state_affair_empty_ambassador_users)
        }
    }

    private fun setFlashatMinistersAdapter() {
        flashatMinistersAdapter = StateAffairCitizenshipUserPagingAdapter(false,this)

        binding.flashatMinisters.listRecycler.apply {
            setHasFixedSize(true)
            adapter = flashatMinistersAdapter
        }

        flashatMinistersAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.flashatMinisters.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.flashatMinisters.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_state_affair_empty)
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.state_affair_empty_minister_users)
        }
    }

    private fun setTribesAdapter() {
        strongestTribeAdapter = StateAffairTribePagingAdapter(false,this)

        binding.strongestTribes.listRecycler.apply {
            setHasFixedSize(true)
            adapter = strongestTribeAdapter
        }

        strongestTribeAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.strongestTribes.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.strongestTribes.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_state_affair_empty)
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.state_affair_empty_tribes)
        }
    }


    fun observe(){
        viewModel.presidentId.observe(viewLifecycleOwner) { presidentId ->
            Log.d("PRESIDENT_ID", "$presidentId")
            binding.presidentLayout.isVisible= !presidentId.isNullOrEmpty()
            binding.presidentEmptyLayout.isVisible= presidentId.isNullOrEmpty()

            if(presidentId.isNullOrEmpty())return@observe

            Log.d("PRESIDENT_ID", "$presidentId")
            userViewModel.setUserId(
                id = presidentId.toInt(),
                context = UserProfileContext.GENERAL,
                isPopupButtonVisible = false
            )
            userIdCardArgs = PublicUserProfileFragmentArgs(
                id = presidentId.toInt(),
                context = UserProfileContext.GENERAL,
                popOnAction = false
            )
        }

        viewModel.stateAffairTribes.observe(viewLifecycleOwner) {
            Log.d("STR_TRIBE", "$it")
            strongestTribeAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
        viewModel.stateAffairMinisters.observe(viewLifecycleOwner) {
            Log.d("STR_MINI", "$it")
            flashatMinistersAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
        viewModel.stateAffairAmbassadors.observe(viewLifecycleOwner) {
            Log.d("STR_AMBA", "$it")
            flashatAmbassadorsAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
        viewModel.stateAffairOfficers.observe(viewLifecycleOwner) {
            Log.d("STR_OFFI", "$it")
            flashatOfficersAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }

        viewModel.stateAffairSkillFullFlashaters.observe(viewLifecycleOwner) {
            Log.d("STR_SKILL", "$it")
            skillFullFlashatersAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
        viewModel.stateAffairPopularFlashaters.observe(viewLifecycleOwner) {
            Log.d("STR_SKILL", "$it")
            popularFlashatersAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }

        viewModel.stateAffairGenerousFlashaters.observe(viewLifecycleOwner) {
            Log.d("STR_GENER", "$it")
            generousFlashatersAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
        viewModel.StateStateStatistics.observe(viewLifecycleOwner){
            Log.d("COUNT_CITIZENSHIPS", "$it")
            binding.stateStatistics.totalUserCountCitizen.text = it?.citizen?.total.toString()
            binding.stateStatistics.totalUserCountResident.text = it?.resident?.total.toString()
            binding.stateStatistics.totalUserCountVisitor.text = it?.visitor?.total.toString()
            binding.stateStatistics.totalUserCountGolden.text = it?.golden?.total.toString()
            binding.stateStatistics.totalCitizens.background = GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, intArrayOf(0XFFFFF5BB.toInt(),0XFFFFFFFF.toInt()))
            binding.stateStatistics.totalResident.background = GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, intArrayOf(0XFF5E5E5E.toInt(),0XFFEEEEEE.toInt()))
            binding.stateStatistics.totalVisitor.background = GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, intArrayOf(0XFFE0E0E0.toInt(),0XFFE0E0E0.toInt()))
            binding.stateStatistics.totalGolden.background = GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, intArrayOf(0XFFFFD473.toInt(),0XFF6D1B95.toInt()))
        }
        userViewModel.profile.observe(viewLifecycleOwner) { profile ->
            if (profile?.id==null)return@observe
            binding?.presidentCard?.setupIDCard(
                profile = profile,
                context = requireContext(),
                lifecycleOwner = viewLifecycleOwner,
                publicUserProfileArgs = userIdCardArgs,
                viewModel = userViewModel,
                isUserStrengthViewHidden = true,
                presidentIdCardBackground = R.drawable.bg_state_affair_president,
                isIdCardTitleBackgroundVisible = false
            )
        }
    }

    private fun  goToCount(
        stateAffairsTotalUsers: StateAffairsTotalUsers,userCitizenship: UserCitizenship,
        context: Context,
    ){
        showInfo(stateAffairsTotalUsers,userCitizenship,context)
    }


    private fun showInfo(stateAffairsTotalUsers: StateAffairsTotalUsers,userCitizenship: UserCitizenship,context: Context) {
        MaterialDialog(context).show {
            val view = DataBindingUtil.inflate<LayoutStateAffairLowGradedUsersBinding>(layoutInflater, R.layout.layout_state_affair_low_graded_users, null, false)
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view. setData(stateAffairsTotalUsers,userCitizenship, context)

        }
    }

    override fun onProfileClick(item: UserStateAffair) {
        Log.i("SA", "bind: reached fragment")
        Log.i("SA", "bind: reached fragment $item")
        navigateToUserProfile(item.id?:item.userId)
    }


}
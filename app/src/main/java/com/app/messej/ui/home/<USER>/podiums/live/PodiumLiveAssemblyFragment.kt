package com.app.messej.ui.home.publictab.podiums.live

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.appcompat.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.enums.AssemblySpeakingSkipReason
import com.app.messej.data.model.enums.AssemblySpeakingSkippedBy
import com.app.messej.data.model.enums.AssemblySpeakingStatus
import com.app.messej.data.model.enums.AssemblyStopSpeakingReason
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.data.model.enums.TheaterCharge
import com.app.messej.data.utils.BeepUtils.playTone
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.FragmentPodiumLiveAssemblyBinding
import com.app.messej.databinding.ItemPodiumLikesContainerBinding
import com.app.messej.databinding.ItemPodiumLiveChatAboutBinding
import com.app.messej.databinding.ItemPodiumSpeakerHeaderBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.databinding.LayoutPodiumChatPausedEmptyBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePodiumCreateAllowed
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPodiumPromoBoard
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.CHALLENGE_RUNNING
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.DISABLED_BY_ADMIN
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.DISABLED_BY_MANAGER
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.UNKNOWN
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showPodiumCoinInfoDialog
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showPodiumLikeInfoDialog
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.hideKeyboard
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.PermissionsHelper.checkAudioPermission
import com.google.android.material.button.MaterialButton
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kennyc.view.MultiStateView
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.coroutines.cancellation.CancellationException


class PodiumLiveAssemblyFragment : PodiumLiveAbstractFragment() {
    override val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

    val args: PodiumLiveAssemblyFragmentArgs by navArgs()

    override val podiumIdArg: String
        get() = args.podiumId

    override val enableScrollForTab: PodiumTab?
        get() = PodiumTab.fromInt(args.enableScrollForTab)

    private var mSpeakerAdapter: PodiumLiveSpeakersAdapter? = null

    override lateinit var binding: FragmentPodiumLiveAssemblyBinding

    override val liveBindingElements = object: LiveBindingElements {

        override val liveChat: RecyclerView
            get() = binding.liveChat

        override fun showLocalVideoSurface(show: Boolean) {
            // Nothing to do here
        }

        override fun onPodiumLoading(loading: Boolean) {
            binding.speakerListMultiStateView.viewState = if (loading) MultiStateView.ViewState.LOADING else MultiStateView.ViewState.CONTENT
            binding.header.podiumHeaderMultiStateView.viewState = if (loading) MultiStateView.ViewState.LOADING else MultiStateView.ViewState.CONTENT
        }

        override val likesContainer: ItemPodiumLikesContainerBinding
            get() = binding.likesContainer

        override val chatSend: MaterialButton
            get() = binding.chatSendButton

        override val actionPaidLike: MaterialButton
            get() = binding.actionLike

        override val actionShare: MaterialButton? = null

        override val actionDecorHolderTop: LinearLayoutCompat
            get() = binding.actionDecorHolderTop

        override val liveCounter: ViewGroup
            get() = binding.header.liveCounter

        override val scrollerLayout: SmartRefreshLayout
            get() = binding.refreshLayout
    }

    private var assemblyTimerJob: Job? = null
    private var assemblyPausedTimerJob: Job? = null
    private var assemblyOffLineTimerJob: Job? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_live_assembly, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        liveChatAbout = DataBindingUtil.inflate<ItemPodiumLiveChatAboutBinding?>(layoutInflater, R.layout.item_podium_live_chat_about, null, false).apply {
            mLiveChatAdapter?.addFooterView(this.root)
        }
        initAdapter()
        setup()
        addObservers()
    }

    override fun onSecondResume() {

    }

    private fun setup() {
        setEmptyView()
        binding.header.podiumDp.setOnClickListener {
            showMoreMenu(it)
        }
//        binding.header.exitButton.setOnClickListener {
//            onExit()
//        }
        binding.actionSkip.setOnClickListener {
            confirmAction(null, R.string.podium_assembly_skip_speaking_confirm, R.string.common_skip, R.string.common_cancel) {
                viewModel.skipSpeaking()
            }
        }

        binding.actionMuteToggle.setOnClickListener {
            viewModel.muteToggleSelf()
            if (viewModel.speakerZoneActivated.value == true) {
                lifecycleScope.launch {
                    binding.actionMuteToggle.isEnabled = false
                    delay(2000)
                    withContext(Dispatchers.Main) {
                        binding.actionMuteToggle.isEnabled = true
                    }
                }
            }
        }

        setupPodiumPromoBoard(binding.ticker)

        binding.header.likeCounter.setOnClickListener {
            showPodiumLikeInfoDialog()
        }

        binding.header.coinCounter.setOnClickListener {
            if (viewModel.canShowTheaterCharges()) {
                findNavController().navigateSafe(PodiumLiveAssemblyFragmentDirections.actionGlobalPodiumChargesBottomSheetFragment(args.podiumId, TheaterCharge.SPEAKER))
            }else{
                showPodiumCoinInfoDialog()
            }
        }

        binding.inputComment.apply {
            setOnClickListener {
                if (!checkUserCanCommentByRating()) {
                    clearFocus()
                    hideKeyboard()
                }
            }
        }
    }

    override fun onExit() {
        if (viewModel.iAmManager.value == true) {
            confirmClose {
                viewModel.close()
            }
        } else super.onExit()
    }

    private fun onSpeakerItemClick(item: ActiveSpeakerUIModel, view: View, mainScreen: Boolean = false) {
        val action = PodiumLiveAssemblyFragmentDirections.actionPodiumLiveAssemblyFragmentToPodiumSpeakerActionsBottomSheetFragment(item.speaker.id)
        findNavController().navigateSafe(action)
    }

    private lateinit var liveChatAbout: ItemPodiumLiveChatAboutBinding

    private fun initAdapter() {
        Log.d("PLF", "initAdapter: create new adapter")

        mSpeakerAdapter = PodiumLiveSpeakersAdapter(layoutInflater, mutableListOf(), object : PodiumLiveSpeakersAdapter.SpeakerListener {
            override fun onEmptySpeakZoneClick() {
                ensurePodiumCreateAllowed {
                    if (viewModel.showRequestToSpeak.value == true) {
                        if (!checkUserCanSpeakByRating()) return@ensurePodiumCreateAllowed
                        val coinBalance = viewModel.podium.value?.audienceFee ?: 0
                        val message = if (coinBalance == 0) getString(R.string.podium_speaker_request_confirm_message)
                        else getString(R.string.podium_speaking_paid_confirmation_free, "$coinBalance")

                        showSpeakingConfirmationAlert(
                            confirmMessage = message,
                            userCoins = viewModel.user.coinBalance.toInt(),
                            podiumSpeakingFee = coinBalance
                        ) {
                            checkAudioPermission(binding.root) {
                                viewModel.requestToSpeak()
                            }
                        }
                    }
//                    if (viewModel.showRequestToSpeak.value == true) {
//                        confirmAction(null, getString(R.string.podium_speaker_request_confirm_message), R.string.common_confirm, R.string.common_cancel) {
//                            checkCameraPermission(binding.root) {
//                                viewModel.requestToSpeak()
//                            }
//                        }
//                    }
                }
            }

            override fun onActiveSpeakerClick(activeSpeaker: ActiveSpeakerUIModel, view: View) {
                onSpeakerItemClick(activeSpeaker, view)
            }

            override fun setSpeakerTitle(speakerHeader: ItemPodiumSpeakerHeaderBinding, item: ActiveSpeakerUIModel) {
               speakerHeader.setSpeakerTitle(item)
            }

            override fun showAudioControls(speakerId: Int): Boolean {
                return false
            }

            override fun toggleMic(speakerId: Int, isMuted: Boolean) {

            }
        })

        binding.speakerList.apply {
            layoutManager = GridLayoutManager(context, 4).apply {}
            setHasFixedSize(true)
            adapter = mSpeakerAdapter
        }
    }

    @SuppressLint("RestrictedApi")
    private fun addObservers() {

        viewModel.podium.observe(viewLifecycleOwner) {
            liveChatAbout.about = it?.bio.orEmpty()
            liveChatAbout.hostName = it?.managerName.orEmpty()
        }
        viewModel.speakers.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: Speakers $it")
        }
        viewModel.speakersWithPlaceholders.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: speakersFullList ${it.size}")
            mSpeakerAdapter?.updateData(it)
        }
        viewModel.showRequestToSpeak.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: showRequestToSpeak $it")
        }
        viewModel.onAdminMuted.observe(viewLifecycleOwner) {
            //If muted
            if (it.first == true) {
                when (it.second) {
                    true -> showToast(R.string.podium_muted_by_manager_message)
                    false -> showToast(R.string.podium_muted_by_admin_message)
                    else -> {}
                }
            }
        }
        viewModel.chatDisabled.observe(viewLifecycleOwner) {
            binding.liveChatMultiStateView.viewState = MultiStateView.ViewState.CONTENT

            binding.chatTextBoxMultiStateView.viewState = if (it == null) MultiStateView.ViewState.CONTENT
            else if (viewModel.iAmManager.value == true) MultiStateView.ViewState.CONTENT
            else when (it) {
                CHALLENGE_RUNNING -> MultiStateView.ViewState.CONTENT
                DISABLED_BY_MANAGER -> {
                    chatTextBoxEmptyView.edsEmptyMessage.setText(R.string.podium_comments_disabled_manager)
                    MultiStateView.ViewState.EMPTY
                }

                DISABLED_BY_ADMIN -> {
                    chatTextBoxEmptyView.edsEmptyMessage.setText(R.string.podium_comments_disabled_admin)
                    MultiStateView.ViewState.EMPTY
                }

                UNKNOWN -> {
                    chatTextBoxEmptyView.edsEmptyMessage.setText(R.string.podium_comments_disabled)
                    MultiStateView.ViewState.EMPTY
                }
            }
        }

        viewModel.onNewSpeakRequest.observe(viewLifecycleOwner) {
            playTone(R.raw.podium_sound_two)
        }

        viewModel.onAssemblyPausedSpeaking.observe(viewLifecycleOwner) {
            viewModel.currentActiveSpeaker = null
            if(assemblyPausedTimerJob?.isActive != true) {
                startTimerForAssemblyPaused(it)
            }
        }

        viewModel.isCurrentAssemblySpeaker.observe(viewLifecycleOwner) {
            Log.w("PLF", "observe:currentAssemblySpeaker: $it ", )
        }

        viewModel.hasPausedAssemblySpeaker.observe(viewLifecycleOwner) {
            Log.w("PLF", "observe:hasPausedAssemblySpeaker: $it ",)
        }


        viewModel.onAssemblySpeakingTimeExtended.observe(viewLifecycleOwner) {
            showToast(R.string.podium_assembly_speaking_time_extended)
        }

        viewModel.iAmPaused.observe(viewLifecycleOwner) {
            showToast(R.string.podium_assembly_your_timer_has_been_paused)
        }

        viewModel.myTurnStarted.observe(viewLifecycleOwner) {
            playTone(R.raw.assembly_podium_start_speaking)
        }

        viewModel.startTimerForAssemblySpeaking.observe(viewLifecycleOwner) {
            if(it == null || it <= 0) return@observe
            assemblyTimerJob?.cancel()
            assemblyPausedTimerJob?.cancel()
            startTimerForAssembly(it)
        }
        viewModel.isLikeLoading.observe(viewLifecycleOwner){
            Log.d("LIKE_ENABLED", "observe: $it")
        }
    }

    override fun showMoreMenu(v: View): PopupMenu {
        return super.showMoreMenu(v).apply {
            menu.apply {
                findItem(R.id.action_admins).isVisible = false
//                findItem(R.id.action_leave).isVisible = viewModel.iAmManager.value != true
                findItem(R.id.action_pause_resume_mic).isVisible = false
                findItem(R.id.action_send_invitations).isVisible = false
            }
        }
    }

    private lateinit var liveChatEmptyView: LayoutListStateEmptyBinding
    private lateinit var chatTextBoxEmptyView: LayoutPodiumChatPausedEmptyBinding

    fun setEmptyView() {
        val emptyView: View = binding.liveChatMultiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        liveChatEmptyView = LayoutListStateEmptyBinding.bind(emptyView).apply {
            prepare(message = R.string.podium_comments_disabled)
        }

        val textBoxEmptyView: View = binding.chatTextBoxMultiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        chatTextBoxEmptyView = LayoutPodiumChatPausedEmptyBinding.bind(textBoxEmptyView)
    }

    override fun toLiveChatActions(userId: Int) {
        val action = PodiumLiveAssemblyFragmentDirections.actionPodiumLiveAssemblyFragmentToPodiumLiveChatActionsBottomSheetFragment(userId)
        findNavController().navigateSafe(action)
    }

    override fun toLiveUsersList() {
        val action = PodiumLiveAssemblyFragmentDirections.actionPodiumLiveAssemblyFragmentToPodiumLiveUsersListBottomSheetFragment()
        findNavController().navigateSafe(action)
    }

    override fun toAdminList() {
        val action = PodiumLiveAssemblyFragmentDirections.actionPodiumLiveAssemblyFragmentToPodiumAdminsBottomSheetFragment()
        findNavController().navigateSafe(action)
    }

    override fun toRestrictedUsers() {
        val action = PodiumLiveAssemblyFragmentDirections.actionPodiumLiveAssemblyFragmentToPodiumBlockedUsersBottomSheetFragment()
        findNavController().navigateSafe(action)
    }

    override fun toInvitations() {
        val action = PodiumLiveAssemblyFragmentDirections.actionPodiumLiveAssemblyFragmentToPodiumInviteBottomSheetFragment(podiumId = podiumIdArg)
        findNavController().navigateSafe(action)
    }

    override fun onAboutToJoin() {
        val builder = MaterialAlertDialogBuilder(requireContext()).apply {
            setMessage(getString(R.string.podium_assembly_join_popup_message))
            setNegativeButton(R.string.common_close) { dialog, _ ->
                dialog.dismiss()
            }
        }
        builder.show()
    }

    override fun toBuyCamera(buy: Boolean) {
        findNavController().navigateSafe(PodiumLiveAssemblyFragmentDirections.actionPodiumLiveAssemblyFragmentToPodiumBuyCameraBottomSheetFragment(buy))
    }

    private fun startTimerForAssemblyPaused(remainingMs: Long) {
        assemblyPausedTimerJob?.cancel()
        assemblyTimerJob?.cancel()
        DateTimeUtils.countDownTimerFlow(remainingMs).onEach {
            viewModel.notifyAssemblySpeakingTimerValue(it.toLong())
        }.onCompletion {
            if (it == null) {
                viewModel.clearAssemblySpeakingTimer()
                viewModel.muteToggleSelf()
                viewModel.resetCurrentActiveSpeaker()
            }
        }.launchIn(lifecycleScope).apply {
            assemblyPausedTimerJob = this
        }
    }

    private fun startTimerForAssembly(remainingMs: Long) {
        val currentSpeaker = viewModel.currentActiveSpeaker
        assemblyTimerJob?.cancel()
        DateTimeUtils.countDownTimerFlow(remainingMs).onEach {
            val stillWaiting = viewModel.speakers.value?.find { ps-> ps.speaker.id == viewModel.currentAssemblySpeaker.value?.id}?.speaker?.speakingStatus == AssemblySpeakingStatus.WAITING
            if(stillWaiting) {
                if (((remainingMs/1000) - 10).toInt() >= it) {
                    Log.d("PLF","startTimerForAssembly: 10 seconds-SKIP")
                    viewModel.onManagerSkipEnabled.postValue(true)
                }

                if (((remainingMs/1000) - 20).toInt() >= it) {
                    Log.d("PLF","startTimerForAssembly: 20 seconds-STOP")
                    viewModel.stopSpeaking(AssemblyStopSpeakingReason.SKIP, skippedBy = AssemblySpeakingSkippedBy.SYSTEM, skipReason = AssemblySpeakingSkipReason.SKIPPED_INACTIVE_SPEAKER)
                }
            }
            viewModel.notifyAssemblySpeakingTimerValue(it.toLong())
        }.onCompletion {
            if (it == null) {
                viewModel.clearAssemblySpeakingTimer()
                startOfflineTimerForAssembly(currentSpeaker)
                // only call onComplete if the job was not cancelled
                if (viewModel.isCurrentAssemblySpeaker.value == true) {
                    if (viewModel.hasPausedAssemblySpeaker.value == true) {
                        viewModel.muteToggleSelf()
                    } else {
                        viewModel.stopSpeaking(AssemblyStopSpeakingReason.TIME_OVER)
                    }
                    viewModel.resetCurrentActiveSpeaker()
                }
                viewModel.resetCurrentActiveSpeaker()
            }
        }.launchIn(lifecycleScope).apply {
            assemblyTimerJob = this
        }
    }

    private fun startOfflineTimerForAssembly(currentSpeaker : Int?) {
        assemblyOffLineTimerJob?.cancel()
        DateTimeUtils.countDownTimerFlow(3 * 1000).onEach {
        }.onCompletion { cause ->
            if (cause is CancellationException) {
                // Flow was canceled
                println("Flow was canceled")
            } else {
                // Flow completed normally
                currentSpeaker?.let {currentSpeaker ->
                    if (currentSpeaker == viewModel.currentAssemblySpeaker.value?.id && viewModel.fetchAssemblySpeakingStatus(currentSpeaker) != AssemblySpeakingStatus.INACTIVE) {
                        Log.i("PLF", "startOfflineTimerForAssembly : speakerId $currentSpeaker")
                        viewModel.stopSpeaking(AssemblyStopSpeakingReason.TIME_OVER)
                    }
                }
            }
        }.launchIn(lifecycleScope).apply {
            assemblyOffLineTimerJob = this
        }
    }
}
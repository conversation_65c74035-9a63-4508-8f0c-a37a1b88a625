package com.app.messej.ui.home.publictab.authorities.stateAffairs

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.paging.LoadState
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.enums.StateAffairsTypes
import com.app.messej.databinding.FragmentStateAffairListViewAllBinding
import com.app.messej.databinding.LayoutIdCardToolTipInformationBinding
import com.app.messej.ui.home.publictab.authorities.stateAffairs.adapters.StateAffairCitizenshipUserPagingAdapter
import com.app.messej.ui.home.publictab.authorities.stateAffairs.adapters.StateAffairFlashatersPagingAdapter
import com.app.messej.ui.home.publictab.authorities.stateAffairs.adapters.StateAffairTribePagingAdapter
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView
import java.util.Locale

class StateAffairListViewAllFragment : Fragment(),StateAffairFlashatersPagingAdapter.stateAffairActionListener,StateAffairCitizenshipUserPagingAdapter.stateAffairActionListener,StateAffairTribePagingAdapter.stateAffairActionListener {


    private val viewModel: StateAffairsMainViewModel by navGraphViewModels(R.id.nav_graph_state_affairs)
    private lateinit var binding: FragmentStateAffairListViewAllBinding
    private val args: StateAffairListViewAllFragmentArgs by navArgs()

    private  var strongestTribeAdapter: StateAffairTribePagingAdapter? = null
    private var flashatMinistersAdapter: StateAffairCitizenshipUserPagingAdapter? = null
    private var flashatAmbassadorsAdapter: StateAffairCitizenshipUserPagingAdapter? = null
    private var flashatOfficersAdapter: StateAffairCitizenshipUserPagingAdapter? = null
    private var skillFullFlashatersAdapter: StateAffairFlashatersPagingAdapter? = null
    private var popularFlashatersAdapter: StateAffairFlashatersPagingAdapter? = null
    private var generousFlashatersAdapter: StateAffairFlashatersPagingAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_state_affair_list_view_all, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolBarTitle.text = args.stateAffairtype.setTitleFromArgs()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {
        setInfoVisibility(args.stateAffairtype)
        setUpNewAdapter()
        binding.imgStateAffairInfo.setOnClickListener {
            infoClickAction(args.stateAffairtype)
        }
    }
    private fun navigateToUserProfile(userId: Int?) {
        userId?.let {
            findNavController().navigateSafe(
                NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(it, false)
            )
        }
    }

    private fun observe(){
        viewModel.stateAffairTribes.observe(viewLifecycleOwner) {
            strongestTribeAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
        viewModel.stateAffairMinisters.observe(viewLifecycleOwner) {
            Log.d("STR_MINI", "$it")
            flashatMinistersAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
        viewModel.stateAffairAmbassadors.observe(viewLifecycleOwner) {
            Log.d("STR_AMBA", "$it")
            flashatAmbassadorsAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
        viewModel.stateAffairOfficers.observe(viewLifecycleOwner) {
            Log.d("STR_OFFI", "$it")
            flashatOfficersAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
        viewModel.stateAffairSkillFullFlashaters.observe(viewLifecycleOwner) {
            Log.d("STR_SKILL", "$it")
            skillFullFlashatersAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
        viewModel.stateAffairPopularFlashaters.observe(viewLifecycleOwner) {
            Log.d("STR_POPULAR", "$it")
            popularFlashatersAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }

        viewModel.stateAffairGenerousFlashaters.observe(viewLifecycleOwner) {
            Log.d("STR_GENER", "$it")
            generousFlashatersAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }

    private fun setUpNewAdapter() {
        when(args.stateAffairtype) {
            StateAffairsTypes.STRONGEST_TRIBES -> {

                strongestTribeAdapter = StateAffairTribePagingAdapter(isLargeScreen = true,this)

                binding.listViewAllRecycler.apply {
                    setHasFixedSize(true)
                    adapter = strongestTribeAdapter
                }

                strongestTribeAdapter?.apply {
                    addLoadStateListener { loadState ->
                        binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                        else if (loadState.append.endOfPaginationReached) {
                            if (itemCount < 1) MultiStateView.ViewState.EMPTY
                            else MultiStateView.ViewState.CONTENT
                        } else MultiStateView.ViewState.CONTENT
                    }
                }

                binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
                    findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_state_affair_empty)
                    findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.state_affair_empty_tribes)
                }
            }


            /**Flashat minister*/
            StateAffairsTypes.FLASHAT_MINISTERS -> {
                flashatMinistersAdapter = StateAffairCitizenshipUserPagingAdapter(true,this)

                binding.listViewAllRecycler.apply {
                    setHasFixedSize(true)
                    adapter = flashatMinistersAdapter
                }

                flashatMinistersAdapter?.apply {
                    addLoadStateListener { loadState ->
                        binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                        else if (loadState.append.endOfPaginationReached) {
                            if (itemCount < 1) MultiStateView.ViewState.EMPTY
                            else MultiStateView.ViewState.CONTENT
                        } else MultiStateView.ViewState.CONTENT
                    }
                }

                binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
                    findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_state_affair_empty)
                    findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.state_affair_empty_minister_users)
                }
            }


                    /**Flashat ambassadors*/
            StateAffairsTypes.FLASHAT_AMBASSADORS -> {
                flashatAmbassadorsAdapter = StateAffairCitizenshipUserPagingAdapter(true,this)

                binding.listViewAllRecycler.apply {
                    setHasFixedSize(true)
                    adapter = flashatAmbassadorsAdapter
                }

                flashatAmbassadorsAdapter?.apply {
                    addLoadStateListener { loadState ->
                        binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                        else if (loadState.append.endOfPaginationReached) {
                            if (itemCount < 1) MultiStateView.ViewState.EMPTY
                            else MultiStateView.ViewState.CONTENT
                        } else MultiStateView.ViewState.CONTENT
                    }
                }

                binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
                    findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_state_affair_empty)
                    findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.state_affair_empty_ambassador_users)
                }
            }
                    /**Flashat officers*/
            StateAffairsTypes.FLASHAT_OFFICERS-> {

                flashatOfficersAdapter = StateAffairCitizenshipUserPagingAdapter(true,this)

                binding.listViewAllRecycler.apply {
                    setHasFixedSize(true)
                    adapter = flashatOfficersAdapter
                }

                flashatOfficersAdapter?.apply {
                    addLoadStateListener { loadState ->
                        binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                        else if (loadState.append.endOfPaginationReached) {
                            if (itemCount < 1) MultiStateView.ViewState.EMPTY
                            else MultiStateView.ViewState.CONTENT
                        } else MultiStateView.ViewState.CONTENT
                    }
                }

                binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
                    findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_state_affair_empty)
                    findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.state_affair_empty_officer_users)
                }
            }
            StateAffairsTypes.MOST_SKILLFUL_FLASHATERS->{
                skillFullFlashatersAdapter = StateAffairFlashatersPagingAdapter(true,StateAffairsTypes.MOST_SKILLFUL_FLASHATERS,this)

                binding.listViewAllRecycler.apply {
                    setHasFixedSize(true)
                    adapter = skillFullFlashatersAdapter
                }

                skillFullFlashatersAdapter?.apply {
                    addLoadStateListener { loadState ->
                        binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                        else if (loadState.append.endOfPaginationReached) {
                            if (itemCount < 1) MultiStateView.ViewState.EMPTY
                            else MultiStateView.ViewState.CONTENT
                        } else MultiStateView.ViewState.CONTENT
                    }
                }

                binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
                    findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_state_affair_empty)
                    findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.state_affair_empty_skillful_members)
                }
            }
            StateAffairsTypes.MOST_POPULAR_FLASHATERS->{
                popularFlashatersAdapter = StateAffairFlashatersPagingAdapter(
                    isLargeScreen = true,
                    listType = StateAffairsTypes.MOST_POPULAR_FLASHATERS,
                    this
                )

                binding.listViewAllRecycler.apply {
                    setHasFixedSize(true)
                    adapter = popularFlashatersAdapter
                }

                popularFlashatersAdapter?.apply {
                    addLoadStateListener { loadState ->
                        binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                        else if (loadState.append.endOfPaginationReached) {
                            if (itemCount < 1) MultiStateView.ViewState.EMPTY
                            else MultiStateView.ViewState.CONTENT
                        } else MultiStateView.ViewState.CONTENT
                    }
                }

                binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
                    findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_state_affair_empty)
                    findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.state_affair_empty_popular_members)
                }

            }
            StateAffairsTypes.MOST_GENEROUS_FLASHATERS->{
                generousFlashatersAdapter = StateAffairFlashatersPagingAdapter(
                    isLargeScreen = true,
                    listType = StateAffairsTypes.MOST_GENEROUS_FLASHATERS,
                    this
                )

                binding.listViewAllRecycler.apply {
                    setHasFixedSize(true)
                    adapter = generousFlashatersAdapter
                }

                generousFlashatersAdapter?.apply {
                    addLoadStateListener { loadState ->
                        binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                        else if (loadState.append.endOfPaginationReached) {
                            if (itemCount < 1) MultiStateView.ViewState.EMPTY
                            else MultiStateView.ViewState.CONTENT
                        } else MultiStateView.ViewState.CONTENT
                    }
                }

                binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
                    findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_state_affair_empty)
                    findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.state_affair_empty_generous_members)
                }
            }
            else->{}
        }
    }


    private fun setInfoVisibility(stateType: StateAffairsTypes) {
        binding.textStateAffairInfo.text= stateType.setInfoData()
        when (stateType) {
            StateAffairsTypes.STRONGEST_TRIBES -> {
                binding.imgStateAffairInfo.visibility = View.GONE
                binding.textStateAffairDesc.text = getString(R.string.state_affair_tribe_desc)
            }
            StateAffairsTypes.FLASHAT_MINISTERS, StateAffairsTypes.FLASHAT_AMBASSADORS, StateAffairsTypes.FLASHAT_OFFICERS, StateAffairsTypes.FLASHAT_STATE_STATISTICS -> {
                binding.layoutViewAllInfo.visibility = View.GONE
            }
            StateAffairsTypes.MOST_GENEROUS_FLASHATERS, StateAffairsTypes.MOST_POPULAR_FLASHATERS, StateAffairsTypes.MOST_SKILLFUL_FLASHATERS -> {
                binding.textStateAffairDesc.visibility = View.GONE
            }
        }
    }

    private fun StateAffairsTypes.setTitleFromArgs():String {
       return when (this) {
            StateAffairsTypes.STRONGEST_TRIBES -> getString(R.string.state_affair_title_strongest_tribes)
            StateAffairsTypes.FLASHAT_MINISTERS -> getString(R.string.state_affair_title_flashat_ministers,String.format(Locale.US, viewModel.ministerCount.value.toString()))
            StateAffairsTypes.FLASHAT_AMBASSADORS ->getString(R.string.state_affair_title_flashat_ambassadors,String.format(Locale.US,viewModel.ambassadorCount.value.toString()))
            StateAffairsTypes.FLASHAT_OFFICERS -> getString(R.string.state_affair_title_flashat_officers,String.format(Locale.US, viewModel.officerCount.value.toString()))
            StateAffairsTypes.MOST_GENEROUS_FLASHATERS -> getString(R.string.state_affair_title_most_generous_flashaters)
            StateAffairsTypes.MOST_POPULAR_FLASHATERS ->getString(R.string.state_affair_title_most_popular_flashaters)
            StateAffairsTypes.MOST_SKILLFUL_FLASHATERS -> getString(R.string.state_affair_title_most_skill_full_flashaters)
            StateAffairsTypes.FLASHAT_STATE_STATISTICS ->getString(R.string.state_affair_header_flashat_state_statistics)
        }
    }
    private fun StateAffairsTypes.setInfoData():String {
       return when (this) {
            StateAffairsTypes.STRONGEST_TRIBES ->getString( R.string.state_affair_strongest_tribes_in_flashat)
            StateAffairsTypes.FLASHAT_MINISTERS, StateAffairsTypes.FLASHAT_AMBASSADORS, StateAffairsTypes.FLASHAT_OFFICERS, StateAffairsTypes.FLASHAT_STATE_STATISTICS ->getString(R.string.state_affair_strongest_tribes_in_flashat)
            StateAffairsTypes.MOST_GENEROUS_FLASHATERS -> getString(R.string.state_affair_flashat_s_most_generous_members)
            StateAffairsTypes.MOST_POPULAR_FLASHATERS -> getString(R.string.state_affair_flashat_s_most_popular_members)
            StateAffairsTypes.MOST_SKILLFUL_FLASHATERS -> getString(R.string.state_affair_flashat_s_most_skillful_members)
        }
    }



    private fun infoClickAction(stateType: StateAffairsTypes) {
        when (stateType) {
            StateAffairsTypes.MOST_GENEROUS_FLASHATERS -> {
                showInfo(getString(R.string.state_affair_most_generous_info))
            }
            StateAffairsTypes.MOST_POPULAR_FLASHATERS -> {
                showInfo(getString(R.string.state_affair_most_popular_info))
            }
            StateAffairsTypes.MOST_SKILLFUL_FLASHATERS -> {
                showInfo(getString(R.string.id_card_pl_desc))
            }
            else -> {
                showInfo(getString(R.string.id_card_pl_desc))
            }
        }
    }


    private fun showInfo(header: String) {
        MaterialDialog(requireContext()).show {
            val view1 = DataBindingUtil.inflate<LayoutIdCardToolTipInformationBinding>(layoutInflater, R.layout.layout_id_card_tool_tip_information, null, false)
            view1.textHeader = header
            customView(null, view1.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view1.actionCloseOthers.setOnClickListener {
               dismiss()
            }
        }
    }

    override fun onProfileClick(item: UserStateAffair) {
        Log.i("SA", "bind: reached fragment")
        Log.i("SA", "bind: reached fragment $item")
        navigateToUserProfile(item.id?:item.userId)
    }

}



package com.app.messej.ui.home.publictab.authorities.stateAffairs.adapters

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.databinding.ItemAffairUserLayoutBinding
import com.app.messej.ui.home.publictab.authorities.stateAffairs.UserStateAffair
import com.app.messej.ui.utils.BindingExtensions.bindCitizenshipUserData

class StateAffairCitizenshipUserPagingAdapter(
    private val isLargeScreen: Boolean? = false,
    private val listener: stateAffairActionListener
) : PagingDataAdapter<UserStateAffair, CitizenshipUserViewHolder>(TransactionsDiffi) {

    interface stateAffairActionListener{
        fun onProfileClick(item: UserStateAffair)
    }
    override fun onBindViewHolder(holder: CitizenshipUserViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it, isLargeScreen,listener) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CitizenshipUserViewHolder {
        val binding = ItemAffairUserLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return CitizenshipUserViewHolder(binding)
        }
    }


    class CitizenshipUserViewHolder(private val binding: ItemAffairUserLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: UserStateAffair, isLargeScreen: Boolean?, listener: StateAffairCitizenshipUserPagingAdapter.stateAffairActionListener) {
            binding.stateAffair = item
            binding.bindCitizenshipUserData(item, isLargeScreen)

            // Add click listener to the entire card
            binding.cardLayout.setOnClickListener {
                listener.onProfileClick(item)
                Log.i("SA", "bind: clicked profile")
            }
        }
    }



object TransactionsDiffi : DiffUtil.ItemCallback<UserStateAffair>() {
    override fun areItemsTheSame(oldItem: UserStateAffair, newItem: UserStateAffair) = oldItem.userId == newItem.userId

    override fun areContentsTheSame(oldItem: UserStateAffair, newItem: UserStateAffair) = oldItem == newItem
}

